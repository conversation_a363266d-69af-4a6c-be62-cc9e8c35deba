#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
سكريبت اختبار الأمان السيبراني - اختبار صفحة الدفع الكامل (محدث)
اختبار إدخال بيانات الدفع والضغط على زر الدفع
موقع: ding.com
الهدف: اختبار ما إذا كان Selenium يمكنه إرسال بيانات الدفع
"""

import time
import random
import json
import os
from datetime import datetime
from selenium import webdriver
from selenium.webdriver.common.by import By
from selenium.webdriver.common.keys import Keys
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC
from selenium.webdriver.common.action_chains import ActionChains
from selenium.webdriver.chrome.options import Options
from selenium.webdriver.support.ui import Select
from selenium.common.exceptions import TimeoutException, NoSuchElementException, ElementClickInterceptedException
import logging

# إعداد التسجيل مع كتابة السجلات للملف
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('payment_test.log', encoding='utf-8'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

# تعيين مستوى السجلات للتصحيح
logger.setLevel(logging.DEBUG)

class DingPaymentTesterUpdated:
    def __init__(self):
        """تهيئة اختبار صفحة الدفع"""
        self.driver = None
        self.wait = None
        
        # مسار ملف الكوكيز
        self.cookies_file = "cookies.txt"
        self.cookies_loaded = False
        
        # بيانات البطاقة الوهمية للاختبار
        self.test_card_data = {
            'card_number': '****************',
            'expiry_month': '05',
            'expiry_year': '27',
            'expiry_full': '05/27',
            'cvv': '971',
            'name_on_card': 'Test Security User',
            'zip_code': '12345'
        }
        
        # بيانات الحساب
        self.account_data = {
            'phone': '+************'
        }
        
        # نتائج الاختبار
        self.test_results = {
            'cookies_loaded': False,
            'cookies_count': 0,
            'reached_summary_page': False,
            'clicked_continue_payment': False,
            'reached_payment_page': False,
            'filled_card_number': False,
            'filled_expiry': False,
            'filled_cvv': False,
            'filled_name': False,
            'filled_zip': False,
            'clicked_pay_button': False,
            'payment_submitted': False,
            'error_messages': [],
            'final_url': '',
            'response_received': False
        }
    
    def parse_netscape_cookies(self):
        """تحليل ملف الكوكيز بتنسيق Netscape - محسن"""
        logger.info("🍪 تحليل ملف الكوكيز...")
        cookies = []
        
        try:
            with open(self.cookies_file, 'r', encoding='utf-8') as f:
                for line_num, line in enumerate(f, 1):
                    original_line = line
                    line = line.strip()
                    
                    # تجاهل التعليقات العادية والأسطر الفارغة
                    if not line or (line.startswith('#') and not line.startswith('#HttpOnly_')):
                        continue
                    
                    # تحليل HttpOnly cookies
                    is_http_only = False
                    if line.startswith('#HttpOnly_'):
                        is_http_only = True
                        line = line[10:]  # إزالة #HttpOnly_
                        logger.debug(f"كوكي HttpOnly تم اكتشافه: {line}")
                    
                    # تقسيم الحقول
                    fields = line.split('\t')
                    if len(fields) >= 6:  # تقليل المتطلبات لـ 6 حقول بدلاً من 7
                        try:
                            domain = fields[0].strip()
                            domain_flag = fields[1].strip().lower() == 'true'
                            path = fields[2].strip()
                            secure = fields[3].strip().lower() == 'true'
                            expiry_str = fields[4].strip()
                            name = fields[5].strip()
                            value = fields[6].strip() if len(fields) > 6 else ''
                            
                            # معالجة تاريخ الانتهاء
                            expiry = None
                            if expiry_str and expiry_str.isdigit() and expiry_str != '0':
                                expiry_timestamp = int(expiry_str)
                                current_time = int(time.time())
                                if expiry_timestamp > current_time:
                                    expiry = expiry_timestamp
                                else:
                                    logger.debug(f"كوكي منتهي الصلاحية تم تجاهله: {name}")
                                    continue
                            
                            # تنظيف النطاق
                            clean_domain = domain
                            if domain.startswith('.'):
                                clean_domain = domain[1:]  # إزالة النقطة الأولى
                            
                            # إنشاء كوكي
                            cookie = {
                                'name': name,
                                'value': value,
                                'domain': clean_domain,
                                'path': path
                            }
                            
                            # إضافة الخصائص الاختيارية
                            if expiry:
                                cookie['expiry'] = expiry
                            
                            if secure:
                                cookie['secure'] = True
                                
                            if is_http_only:
                                cookie['httpOnly'] = True
                            
                            # إضافة معلومات إضافية للتشخيص
                            cookie['original_domain'] = domain
                            cookie['domain_flag'] = domain_flag
                                
                            cookies.append(cookie)
                            logger.debug(f"✅ تم تحليل كوكي: {name} للنطاق {clean_domain} (HttpOnly: {is_http_only})")
                            
                        except (ValueError, IndexError) as e:
                            logger.warning(f"⚠️ خطأ في تحليل السطر {line_num}: {e}")
                            logger.debug(f"السطر الأصلي: {original_line}")
                            continue
                    else:
                        logger.debug(f"سطر غير صالح (عدد حقول غير كافي): {line}")
            
            logger.info(f"✅ تم تحليل {len(cookies)} كوكيز بنجاح")
            
            # طباعة إحصائيات
            http_only_count = sum(1 for c in cookies if c.get('httpOnly', False))
            secure_count = sum(1 for c in cookies if c.get('secure', False))
            logger.info(f"📊 إحصائيات الكوكيز: HttpOnly={http_only_count}, Secure={secure_count}")
            
            return cookies
            
        except Exception as e:
            logger.error(f"❌ خطأ في تحليل ملف الكوكيز: {e}")
            return []
    
    def load_cookies(self, force_refresh=True):
        """تحميل الكوكيز في المتصفح - محسن مع طرق متعددة"""
        if self.cookies_loaded and not force_refresh:
            logger.info("🍪 الكوكيز محملة بالفعل - تم تجاهل التحديث")
            return
            
        logger.info("🍪 بدء تحميل الكوكيز في المتصفح...")
        
        try:
            # تحليل الكوكيز
            cookies = self.parse_netscape_cookies()
            if not cookies:
                logger.error("❌ لم يتم العثور على كوكيز للتحميل")
                return
            
            logger.info(f"📋 سيتم تحميل {len(cookies)} كوكيز")
            
            # الذهاب إلى الصفحة الرئيسية أولاً
            logger.info("🌐 الانتقال إلى الصفحة الرئيسية...")
            self.driver.get("https://www.ding.com")
            time.sleep(3)
            
            # الطريقة 1: حقن الكوكيز باستخدام CDP (الأكثر فعالية)
            logger.info("🔧 الطريقة 1: حقن الكوكيز باستخدام CDP...")
            cdp_success = self.inject_cookies_via_cdp()
            
            # الطريقة 2: تحميل الكوكيز باستخدام Selenium التقليدي
            logger.info("🔧 الطريقة 2: تحميل الكوكيز باستخدام Selenium...")
            
            # تصنيف الكوكيز حسب النطاق
            domain_cookies = {}
            for cookie in cookies:
                domain = cookie['domain']
                original_domain = cookie.get('original_domain', domain)
                
                # استخدام النطاق الأصلي للتجميع
                key_domain = original_domain
                if key_domain not in domain_cookies:
                    domain_cookies[key_domain] = []
                domain_cookies[key_domain].append(cookie)
            
            logger.info(f"🌐 تم تجميع الكوكيز في {len(domain_cookies)} نطاق/نطاقات")
            
            # قائمة النطاقات للزيارة
            domains_to_visit = [
                "https://www.ding.com",
                "https://ding.com"
            ]
            
            successful_cookies = 0
            
            # تحميل الكوكيز لكل نطاق
            for visit_url in domains_to_visit:
                try:
                    logger.info(f"🌐 زيارة النطاق: {visit_url}")
                    self.driver.get(visit_url)
                    time.sleep(2)
                    
                    # تحميل جميع الكوكيز المناسبة لهذا النطاق
                    for domain_key, domain_cookies_list in domain_cookies.items():
                        for cookie in domain_cookies_list:
                            try:
                                # تحضير الكوكي للإضافة
                                clean_cookie = {
                                    'name': cookie['name'],
                                    'value': cookie['value'],
                                    'path': cookie['path']
                                }
                                
                                # تحديد النطاق المناسب
                                cookie_domain = cookie['domain']
                                if not cookie_domain.startswith('.'):
                                    clean_cookie['domain'] = cookie_domain
                                
                                # إضافة الخصائص الاختيارية
                                if cookie.get('secure', False):
                                    clean_cookie['secure'] = True
                                    
                                if 'expiry' in cookie and cookie['expiry']:
                                    clean_cookie['expiry'] = cookie['expiry']
                                
                                # ملاحظة: httpOnly لا يمكن تعيينه عبر Selenium
                                # لكن سيتم تسجيله للمراجعة
                                is_http_only = cookie.get('httpOnly', False)
                                
                                # محاولة إضافة الكوكي
                                self.driver.add_cookie(clean_cookie)
                                successful_cookies += 1
                                
                                status = "🔒 HttpOnly" if is_http_only else "✅ عادي"
                                logger.debug(f"  {status} تم إضافة: {cookie['name']} -> {cookie_domain}")
                                
                            except Exception as e:
                                error_msg = str(e)
                                if "invalid cookie domain" in error_msg.lower():
                                    logger.debug(f"  ⚠️ نطاق غير متوافق: {cookie['name']} -> {cookie.get('domain', 'unknown')}")
                                else:
                                    logger.debug(f"  ⚠️ فشل إضافة {cookie['name']}: {error_msg}")
                                continue
                    
                except Exception as e:
                    logger.error(f"❌ خطأ في زيارة النطاق {visit_url}: {e}")
                    continue
            
            # الطريقة 3: حقن الكوكيز باستخدام JavaScript
            logger.info("🔧 الطريقة 3: حقن الكوكيز باستخدام JavaScript...")
            js_injected = self.inject_cookies_via_javascript()
            
            self.cookies_loaded = True
            self.test_results['cookies_loaded'] = True
            self.test_results['cookies_count'] = successful_cookies
            
            logger.info(f"✅ تم تحميل {successful_cookies} كوكيز من أصل {len(cookies)} بنجاح")
            logger.info(f"📊 CDP: {'✅' if cdp_success else '❌'} | Selenium: {successful_cookies} | JS: {'✅' if js_injected else '❌'}")
            
            # العودة إلى الصفحة الرئيسية وتحديثها (فقط إذا لزم الأمر)
            if force_refresh:
                current_url = self.driver.current_url
                if "ding.com" not in current_url:
                    logger.info("🔄 تحديث الصفحة لتطبيق الكوكيز...")
                    self.driver.get("https://www.ding.com")
                    time.sleep(3)
                else:
                    logger.info("🔄 تحديث الصفحة الحالية لتطبيق الكوكيز...")
                    self.driver.refresh()
                    time.sleep(3)
            else:
                logger.info("⏭️ تم تجاهل تحديث الصفحة للحفاظ على البيانات المدخلة")
            
            # التحقق من تطبيق الكوكيز
            cookies_verification = self.verify_cookies_loaded()
            
            # فحص حالة تسجيل الدخول
            login_status = self.check_login_status()
            if login_status:
                logger.info("✅ تم تسجيل الدخول بنجاح باستخدام الكوكيز")
            elif login_status is False:
                logger.warning("⚠️ الكوكيز لم تؤدي إلى تسجيل الدخول")
            else:
                logger.info("🤔 حالة تسجيل الدخول غير واضحة")
            
        except Exception as e:
            logger.error(f"❌ خطأ في تحميل الكوكيز: {e}")
    
    def verify_cookies_loaded(self):
        """التحقق من تحميل الكوكيز بنجاح - محسن"""
        try:
            current_cookies = self.driver.get_cookies()
            loaded_cookie_names = [cookie['name'] for cookie in current_cookies]
            
            logger.info(f"📊 إجمالي الكوكيز المحملة في المتصفح: {len(current_cookies)}")
            
            # طباعة جميع الكوكيز المحملة للتشخيص
            if current_cookies:
                logger.info("🍪 قائمة الكوكيز المحملة:")
                for i, cookie in enumerate(current_cookies, 1):
                    value_preview = cookie['value'][:30] + "..." if len(cookie['value']) > 30 else cookie['value']
                    domain = cookie.get('domain', 'غير محدد')
                    secure = "🔒" if cookie.get('secure', False) else "🔓"
                    logger.info(f"  {i:2d}. {secure} {cookie['name']} = {value_preview} (النطاق: {domain})")
            
            # البحث عن كوكيز مهمة للتأكد من تحميلها
            important_cookies = [
                'ding_ssn',      # كوكي الجلسة الرئيسي
                'eze_track',     # كوكي التتبع
                'DeviceId',      # معرف الجهاز
                'ding_crt',      # كوكي المصادقة
                '_ga',           # Google Analytics
                'bh',            # Browser Hash
                '__cf_bm',       # Cloudflare Bot Management
                'OptanonConsent' # كوكي الموافقة
            ]
            
            found_important = []
            missing_important = []
            
            for important_cookie in important_cookies:
                if important_cookie in loaded_cookie_names:
                    found_important.append(important_cookie)
                else:
                    missing_important.append(important_cookie)
            
            # تقرير الكوكيز المهمة
            if found_important:
                logger.info(f"✅ الكوكيز المهمة الموجودة ({len(found_important)}):")
                for cookie_name in found_important:
                    cookie_data = next((c for c in current_cookies if c['name'] == cookie_name), None)
                    if cookie_data:
                        value_preview = cookie_data['value'][:20] + "..." if len(cookie_data['value']) > 20 else cookie_data['value']
                        logger.info(f"  ✓ {cookie_name}: {value_preview}")
            
            if missing_important:
                logger.warning(f"⚠️ الكوكيز المهمة المفقودة ({len(missing_important)}):")
                for cookie_name in missing_important:
                    logger.warning(f"  ✗ {cookie_name}")
            
            # فحص كوكيز إضافية مثيرة للاهتمام
            interesting_patterns = ['ding_', 'eze_', '_ga', '__cf', 'Device', 'session']
            interesting_cookies = []
            
            for cookie_name in loaded_cookie_names:
                for pattern in interesting_patterns:
                    if pattern.lower() in cookie_name.lower():
                        interesting_cookies.append(cookie_name)
                        break
            
            if interesting_cookies:
                logger.info(f"🔍 كوكيز إضافية مثيرة للاهتمام ({len(interesting_cookies)}):")
                for cookie_name in interesting_cookies[:5]:  # أول 5 فقط
                    logger.info(f"  • {cookie_name}")
            
            # تقييم نجاح التحميل
            success_score = len(found_important) / len(important_cookies) * 100
            logger.info(f"📈 نسبة نجاح تحميل الكوكيز المهمة: {success_score:.1f}%")
            
            # اعتبار التحميل ناجحاً إذا تم العثور على 50% على الأقل من الكوكيز المهمة
            if success_score >= 50:
                logger.info("✅ تم تحميل الكوكيز بنجاح")
                return True
            else:
                logger.warning("⚠️ تحميل الكوكيز غير مكتمل")
                return False
                
        except Exception as e:
            logger.error(f"❌ خطأ في التحقق من الكوكيز: {e}")
            return False
    
    def check_login_status(self):
        """فحص حالة تسجيل الدخول - محسن"""
        try:
            # قائمة موسعة من مؤشرات تسجيل الدخول
            login_indicators = {
                'logged_in': [
                    # روابط وأزرار تسجيل الخروج
                    "//a[contains(@href, 'logout')]",
                    "//button[contains(text(), 'Logout')]",
                    "//button[contains(text(), 'Sign Out')]",
                    "//a[contains(text(), 'Logout')]",
                    "//a[contains(text(), 'Sign Out')]",
                    
                    # عناصر الحساب الشخصي
                    "//*[contains(text(), 'My Account')]",
                    "//*[contains(text(), 'Profile')]",
                    "//*[contains(text(), 'My Profile')]",
                    "//*[contains(@class, 'user-profile')]",
                    "//*[contains(@class, 'account-info')]",
                    
                    # عناصر خاصة بـ Ding.com
                    "//*[contains(@class, 'user-menu')]",
                    "//*[contains(@class, 'account-dropdown')]",
                    "//div[contains(@class, 'logged-in')]",
                    "//*[contains(@class, 'user-avatar')]"
                ],
                'logged_out': [
                    # نماذج وأزرار تسجيل الدخول
                    "//form[contains(@class, 'login')]",
                    "//form[contains(@action, 'login')]",
                    "//input[@type='email']",
                    "//input[@type='password']",
                    
                    # روابط وأزرار تسجيل الدخول
                    "//a[contains(text(), 'Sign in')]",
                    "//a[contains(text(), 'Login')]",
                    "//button[contains(text(), 'Sign in')]",
                    "//button[contains(text(), 'Login')]",
                    
                    # عناصر خاصة بـ Ding.com
                    "//div[contains(@class, 'login-form')]",
                    "//div[contains(@class, 'sign-in')]",
                    "//*[contains(@class, 'guest-user')]"
                ]
            }
            
            logger.info("🔍 فحص حالة تسجيل الدخول...")
            
            # البحث عن مؤشرات تسجيل الدخول
            logged_in_elements = []
            for xpath in login_indicators['logged_in']:
                try:
                    elements = self.driver.find_elements(By.XPATH, xpath)
                    visible_elements = [e for e in elements if e.is_displayed()]
                    if visible_elements:
                        for element in visible_elements:
                            element_text = element.text.strip() if element.text else element.get_attribute('class')
                            logged_in_elements.append(f"{xpath} -> {element_text}")
                except Exception as e:
                    continue
            
            # البحث عن مؤشرات عدم تسجيل الدخول
            logged_out_elements = []
            for xpath in login_indicators['logged_out']:
                try:
                    elements = self.driver.find_elements(By.XPATH, xpath)
                    visible_elements = [e for e in elements if e.is_displayed()]
                    if visible_elements:
                        for element in visible_elements:
                            element_text = element.text.strip() if element.text else element.get_attribute('class')
                            logged_out_elements.append(f"{xpath} -> {element_text}")
                except Exception as e:
                    continue
            
            # تحليل النتائج
            if logged_in_elements:
                logger.info("✅ تم العثور على مؤشرات تسجيل الدخول:")
                for element in logged_in_elements[:3]:  # عرض أول 3 مؤشرات فقط
                    logger.info(f"  ✓ {element}")
                
                if logged_out_elements:
                    logger.warning("⚠️ تم العثور أيضاً على مؤشرات عدم تسجيل الدخول (قد يكون خطأ في واجهة المستخدم)")
                
                return True
                
            elif logged_out_elements:
                logger.warning("❌ تم العثور على مؤشرات عدم تسجيل الدخول:")
                for element in logged_out_elements[:3]:  # عرض أول 3 مؤشرات فقط
                    logger.warning(f"  ✗ {element}")
                return False
                
            else:
                logger.info("🤔 لم يتم العثور على أي مؤشرات لحالة تسجيل الدخول")
                
                # فحص إضافي للكوكيز المتعلقة بتسجيل الدخول
                auth_cookies = ['ding_ssn', 'ding_crt', 'DeviceId']
                current_cookies = [cookie['name'] for cookie in self.driver.get_cookies()]
                found_auth_cookies = [cookie for cookie in auth_cookies if cookie in current_cookies]
                
                if found_auth_cookies:
                    logger.info(f"ℹ️ تم العثور على كوكيز المصادقة: {found_auth_cookies}")
                    return True
                else:
                    logger.info("ℹ️ لم يتم العثور على كوكيز المصادقة")
                    return None
            
        except Exception as e:
            logger.error(f"❌ خطأ في فحص حالة تسجيل الدخول: {e}")
            return None
    
    def inject_cookies_via_javascript(self):
        """حقن الكوكيز باستخدام JavaScript"""
        logger.info("🔧 حقن الكوكيز باستخدام JavaScript...")
        
        try:
            # تحليل الكوكيز
            cookies = self.parse_netscape_cookies()
            if not cookies:
                logger.error("❌ لم يتم العثور على كوكيز للحقن")
                return False
            
            successful_injections = 0
            
            for cookie in cookies:
                try:
                    # تحضير قيم الكوكي
                    name = cookie['name']
                    value = cookie['value']
                    domain = cookie.get('original_domain', cookie['domain'])
                    path = cookie['path']
                    secure = str(cookie.get('secure', False)).lower()
                    expiry = cookie.get('expiry', '')
                    
                    # إنشاء سكريبت JavaScript لإضافة الكوكي
                    js_script = f"""
                    try {{
                        let cookieStr = "{name}={value}; path={path}";
                        if ("{domain}") cookieStr += "; domain={domain}";
                        if ({secure}) cookieStr += "; secure";
                        if ("{expiry}") cookieStr += "; expires=" + new Date({expiry} * 1000).toUTCString();
                        document.cookie = cookieStr;
                        return true;
                    }} catch (e) {{
                        return false;
                    }}
                    """
                    
                    # تنفيذ السكريبت
                    result = self.driver.execute_script(js_script)
                    
                    if result:
                        successful_injections += 1
                        logger.debug(f"✅ تم حقن كوكي JS: {name} -> {domain}")
                    else:
                        logger.debug(f"⚠️ فشل حقن كوكي JS: {name}")
                    
                except Exception as e:
                    logger.debug(f"⚠️ خطأ في حقن كوكي JS {name}: {e}")
                    continue
            
            logger.info(f"✅ تم حقن {successful_injections} كوكيز باستخدام JavaScript")
            return successful_injections > 0
            
        except Exception as e:
            logger.error(f"❌ خطأ في حقن الكوكيز باستخدام JavaScript: {e}")
            return False
            
    def inject_cookies_via_cdp(self):
        """حقن الكوكيز باستخدام Chrome DevTools Protocol - طريقة متقدمة"""
        logger.info("🔧 حقن الكوكيز باستخدام CDP...")
        
        try:
            # تحليل الكوكيز
            cookies = self.parse_netscape_cookies()
            if not cookies:
                logger.error("❌ لم يتم العثور على كوكيز للحقن")
                return False
            
            successful_injections = 0
            
            for cookie in cookies:
                try:
                    # تحضير بيانات الكوكي لـ CDP
                    cdp_cookie = {
                        'name': cookie['name'],
                        'value': cookie['value'],
                        'domain': cookie.get('original_domain', cookie['domain']),
                        'path': cookie['path'],
                        'secure': cookie.get('secure', False),
                        'httpOnly': cookie.get('httpOnly', False)
                    }
                    
                    # إضافة تاريخ الانتهاء إذا كان متوفراً
                    if 'expiry' in cookie and cookie['expiry']:
                        cdp_cookie['expires'] = cookie['expiry']
                    
                    # حقن الكوكي باستخدام CDP
                    self.driver.execute_cdp_cmd('Network.setCookie', cdp_cookie)
                    successful_injections += 1
                    
                    logger.debug(f"✅ تم حقن كوكي CDP: {cookie['name']} -> {cdp_cookie['domain']}")
                    
                except Exception as e:
                    logger.debug(f"⚠️ فشل حقن كوكي CDP {cookie['name']}: {e}")
                    continue
            
            logger.info(f"✅ تم حقن {successful_injections} كوكيز باستخدام CDP")
            return successful_injections > 0
            
        except Exception as e:
            logger.error(f"❌ خطأ في حقن الكوكيز باستخدام CDP: {e}")
            return False

    def setup_driver(self):
        """إعداد متصفح Chrome - محسن"""
        logger.info("🚀 إعداد متصفح Chrome...")
        
        chrome_options = Options()
        
        # خيارات للبيئة المحدودة
        chrome_options.add_argument("--no-sandbox")
        chrome_options.add_argument("--disable-dev-shm-usage")
        chrome_options.add_argument("--disable-gpu")
        chrome_options.add_argument("--remote-debugging-port=9222")
        chrome_options.add_argument("--disable-extensions")
        chrome_options.add_argument("--disable-web-security")
        chrome_options.add_argument("--allow-running-insecure-content")
        
        # خيارات لتجنب اكتشاف الأتمتة
        chrome_options.add_argument("--disable-blink-features=AutomationControlled")
        chrome_options.add_experimental_option("excludeSwitches", ["enable-automation"])
        chrome_options.add_experimental_option('useAutomationExtension', False)
        
        # خيارات إضافية لتحسين الكوكيز
        chrome_options.add_argument("--enable-features=NetworkService")
        chrome_options.add_argument("--disable-features=VizDisplayCompositor")
        chrome_options.add_argument("--disable-background-timer-throttling")
        chrome_options.add_argument("--disable-backgrounding-occluded-windows")
        chrome_options.add_argument("--disable-renderer-backgrounding")
        
        # تعيين User-Agent طبيعي ومحدث
        chrome_options.add_argument("--user-agent=Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36")
        
        # تعيين حجم النافذة
        chrome_options.add_argument("--window-size=1280,720")
        
        # إعدادات الكوكيز
        prefs = {
            "profile.default_content_setting_values": {
                "cookies": 1,  # السماح بالكوكيز
                "images": 1,
                "javascript": 1,
                "plugins": 1,
                "popups": 0,
                "geolocation": 0,
                "notifications": 0,
                "media_stream": 0,
            },
            "profile.default_content_settings": {
                "cookies": 1
            },
            "profile.cookie_controls_mode": 0
        }
        chrome_options.add_experimental_option("prefs", prefs)
        
        self.driver = webdriver.Chrome(options=chrome_options)
        
        # إخفاء خصائص الأتمتة
        try:
            self.driver.execute_script("Object.defineProperty(navigator, 'webdriver', {get: () => undefined})")
            self.driver.execute_script("Object.defineProperty(navigator, 'plugins', {get: () => [1, 2, 3, 4, 5]})")
            self.driver.execute_script("Object.defineProperty(navigator, 'languages', {get: () => ['en-US', 'en']})")
        except:
            # تجاهل الخطأ إذا كانت الخاصية موجودة مسبقاً
            pass
        
        self.wait = WebDriverWait(self.driver, 20)
        
        logger.info("✅ تم إعداد المتصفح بنجاح")
    
    def navigate_to_summary_page(self):
        """الانتقال إلى صفحة الملخص"""
        logger.info("🌐 بدء الانتقال لصفحة الملخص...")
        
        try:
            # التحقق من حالة تسجيل الدخول أولاً
            current_url = self.driver.current_url
            if "ding.com" not in current_url:
                logger.info("📍 الخطوة 1: الذهاب للصفحة الرئيسية")
                self.driver.get("https://www.ding.com")
                time.sleep(3)

            # فحص حالة تسجيل الدخول
            login_status = self.check_login_status()

            # تحميل الكوكيز فقط إذا لم يكن المستخدم مسجل دخول
            if not login_status:
                logger.info("🔄 المستخدم غير مسجل دخول - سيتم تحميل الكوكيز")
                self.load_cookies(force_refresh=True)

                # إعادة فحص حالة تسجيل الدخول بعد تحميل الكوكيز
                login_status = self.check_login_status()
                if not login_status:
                    logger.warning("⚠️ المستخدم لا يزال غير مسجل دخول بعد تحميل الكوكيز")
            else:
                logger.info("✅ المستخدم مسجل دخول بالفعل - لن يتم تحديث الصفحة")
                # تحميل الكوكيز بدون تحديث الصفحة للحفاظ على البيانات المدخلة
                self.load_cookies(force_refresh=False)
            
            # الخطوة 2: إدخال رقم الهاتف (مع مسح أي رقم موجود مسبقاً)
            logger.info("📍 الخطوة 2: إدخال رقم الهاتف")

            # البحث عن حقل الهاتف مع محاولات متعددة
            phone_input = None
            phone_selectors = [
                "input[type='tel']",
                "input[placeholder*='phone']",
                "input[placeholder*='number']",
                "input[name*='phone']",
                "input[id*='phone']"
            ]

            for selector in phone_selectors:
                try:
                    phone_input = self.wait.until(EC.presence_of_element_located((By.CSS_SELECTOR, selector)))
                    if phone_input.is_displayed() and phone_input.is_enabled():
                        logger.info(f"✅ تم العثور على حقل الهاتف باستخدام: {selector}")
                        break
                    else:
                        phone_input = None
                except:
                    continue

            if not phone_input:
                logger.error("❌ لم يتم العثور على حقل الهاتف")
                return False

            # التحقق من وجود رقم مسبق
            try:
                current_phone = phone_input.get_attribute('value')
                if current_phone:
                    logger.info(f"📱 تم العثور على رقم موجود مسبقاً: '{current_phone}' - سيتم مسحه")
            except Exception as e:
                logger.debug(f"خطأ في قراءة رقم الهاتف الحالي: {e}")
                current_phone = ""

            # حفظ حالة الصفحة قبل أي تعديل
            page_url_before = self.driver.current_url
            logger.info(f"💾 حفظ حالة الصفحة: {page_url_before}")

            # استخدام الطريقة المحسنة لمسح وملء حقل الهاتف
            logger.info(f"📱 مسح الرقم الموجود وكتابة الرقم الجديد: {self.account_data['phone']}")
            success = self.clear_and_fill_phone_field(phone_input, self.account_data['phone'])
            if not success:
                logger.warning("⚠️ فشل في ملء رقم الهاتف بالطرق المحسنة، سيتم المحاولة بالطريقة التقليدية")
                try:
                    # التأكد من أن العنصر ما زال صالحاً
                    if phone_input.is_displayed() and phone_input.is_enabled():
                        phone_input.clear()
                        phone_input.send_keys(self.account_data['phone'])
                    else:
                        # إعادة البحث عن حقل الهاتف
                        for selector in phone_selectors:
                            try:
                                phone_input = self.driver.find_element(By.CSS_SELECTOR, selector)
                                if phone_input.is_displayed() and phone_input.is_enabled():
                                    phone_input.clear()
                                    phone_input.send_keys(self.account_data['phone'])
                                    break
                            except:
                                continue
                except Exception as e:
                    logger.error(f"❌ فشل في ملء رقم الهاتف: {e}")

            # التحقق من عدم تغيير الصفحة
            page_url_after = self.driver.current_url
            if page_url_before != page_url_after:
                logger.warning(f"⚠️ تم تغيير الصفحة من {page_url_before} إلى {page_url_after}")
                # إعادة العثور على حقل الهاتف
                for selector in phone_selectors:
                    try:
                        phone_input = self.wait.until(EC.presence_of_element_located((By.CSS_SELECTOR, selector)))
                        if phone_input.is_displayed() and phone_input.is_enabled():
                            break
                    except:
                        continue

            # التحقق من رقم الهاتف النهائي
            try:
                final_phone = phone_input.get_attribute('value') if phone_input else ""
                if final_phone == self.account_data['phone']:
                    logger.info(f"✅ تم تعيين رقم الهاتف بنجاح: {final_phone}")
                else:
                    logger.warning(f"⚠️ رقم الهاتف غير صحيح. المطلوب: {self.account_data['phone']}, الموجود: {final_phone}")
                    # محاولة إضافية لملء الرقم
                    logger.info("🔄 محاولة إضافية لملء رقم الهاتف...")
                    if phone_input and phone_input.is_displayed() and phone_input.is_enabled():
                        self.clear_and_fill_phone_field(phone_input, self.account_data['phone'])
            except Exception as e:
                logger.error(f"❌ خطأ في التحقق من رقم الهاتف النهائي: {e}")
            
            time.sleep(1)
            
            # الخطوة 3: النقر على زر البدء
            logger.info("📍 الخطوة 3: النقر على زر البدء")
            
            # البحث عن زر البدء بطرق متعددة
            start_selectors = [
                "button[type='submit']",
                "button.btn-primary",
                ".btn-primary"
            ]
            
            start_clicked = False
            for selector in start_selectors:
                try:
                    start_buttons = self.driver.find_elements(By.CSS_SELECTOR, selector)
                    for button in start_buttons:
                        if button.is_displayed() and button.is_enabled():
                            button.click()
                            time.sleep(4)
                            logger.info("✅ تم النقر على زر البدء")
                            start_clicked = True
                            break
                    if start_clicked:
                        break
                except:
                    continue
            
            if not start_clicked:
                # محاولة العثور على أي زر مرئي والنقر عليه
                try:
                    all_buttons = self.driver.find_elements(By.TAG_NAME, "button")
                    for button in all_buttons:
                        if button.is_displayed() and button.is_enabled():
                            button_text = button.text.lower()
                            if any(word in button_text for word in ['send', 'submit', 'continue', 'top', 'up']):
                                button.click()
                                time.sleep(4)
                                logger.info(f"✅ تم النقر على زر: {button.text}")
                                start_clicked = True
                                break
                except:
                    pass
            
            # الخطوة 4: اختيار مبلغ الشحن (أقل مبلغ متاح)
            logger.info("📍 الخطوة 4: اختيار أقل مبلغ شحن متاح")
            current_url = self.driver.current_url
            logger.info(f"الصفحة الحالية: {current_url}")
            
            # البحث عن أزرار المبالغ
            amount_selectors = [
                "button[data-amount]",
                ".amount-btn",
                ".topup-amount",
                "button[class*='amount']",
                ".denomination-btn",
                ".value-btn"
            ]
            
            amount_selected = False
            for selector in amount_selectors:
                try:
                    amount_buttons = self.driver.find_elements(By.CSS_SELECTOR, selector)
                    if amount_buttons:
                        # البحث عن أقل مبلغ متاح
                        min_amount = None
                        min_button = None
                        
                        for button in amount_buttons:
                            if button.is_displayed() and button.is_enabled():
                                button_text = button.text.strip()
                                # استخراج الرقم من النص
                                import re
                                numbers = re.findall(r'\d+', button_text)
                                if numbers:
                                    amount_value = int(numbers[0])
                                    if min_amount is None or amount_value < min_amount:
                                        min_amount = amount_value
                                        min_button = button
                        
                        if min_button:
                            min_button.click()
                            time.sleep(2)
                            logger.info(f"✅ تم اختيار أقل مبلغ شحن: {min_button.text}")
                            amount_selected = True
                            break
                except Exception as e:
                    logger.debug(f"خطأ في محاولة اختيار المبلغ: {e}")
                    continue
            
            if not amount_selected:
                # محاولة النقر على أي زر يحتوي على رقم أو عملة (اختيار الأقل)
                try:
                    # انتظار قصير للتأكد من تحديث الصفحة
                    time.sleep(2)

                    # إعادة البحث عن الأزرار لتجنب stale elements
                    all_buttons = self.driver.find_elements(By.TAG_NAME, "button")
                    amount_buttons_with_values = []

                    for button in all_buttons:
                        try:
                            # التأكد من أن العنصر ما زال صالحاً
                            if button.is_displayed() and button.is_enabled():
                                button_text = button.text.strip()
                                if any(char.isdigit() for char in button_text) or 'DZD' in button_text:
                                    # استخراج الرقم
                                    import re
                                    numbers = re.findall(r'\d+', button_text)
                                    if numbers:
                                        amount_value = int(numbers[0])
                                        amount_buttons_with_values.append((amount_value, button))
                        except Exception as button_error:
                            # تجاهل الأزرار التي أصبحت stale
                            logger.debug(f"تجاهل زر غير صالح: {button_error}")
                            continue

                    if amount_buttons_with_values:
                        # ترتيب حسب القيمة واختيار الأقل
                        amount_buttons_with_values.sort(key=lambda x: x[0])
                        min_amount, min_button = amount_buttons_with_values[0]

                        # التأكد من أن الزر ما زال صالحاً قبل النقر
                        try:
                            if min_button.is_displayed() and min_button.is_enabled():
                                min_button.click()
                                time.sleep(2)
                                logger.info(f"✅ تم اختيار أقل مبلغ: {min_amount}")
                                amount_selected = True
                            else:
                                logger.warning("⚠️ الزر المحدد غير متاح للنقر")
                        except Exception as click_error:
                            logger.warning(f"⚠️ فشل النقر على زر المبلغ: {click_error}")
                    else:
                        logger.warning("⚠️ لم يتم العثور على أزرار مبالغ صالحة")

                except Exception as e:
                    logger.debug(f"خطأ في البحث عن أقل مبلغ: {e}")
                    pass
            
            # انتظار الوصول لصفحة الملخص مع محاولات متعددة
            max_wait_attempts = 10
            for attempt in range(max_wait_attempts):
                time.sleep(2)
                current_url = self.driver.current_url
                logger.info(f"محاولة {attempt + 1}/{max_wait_attempts} - الصفحة الحالية: {current_url}")

                # فحص عدة مؤشرات للوصول لصفحة الملخص
                if any(keyword in current_url.lower() for keyword in ["summary", "checkout", "review", "confirm"]):
                    logger.info("✅ تم الوصول لصفحة الملخص بنجاح!")
                    self.test_results['reached_summary_page'] = True
                    return True

                # فحص وجود عناصر تدل على صفحة الملخص
                try:
                    summary_indicators = self.driver.find_elements(By.XPATH,
                        "//h1[contains(text(), 'Summary')] | //h2[contains(text(), 'Summary')] | " +
                        "//button[contains(text(), 'Continue to payment')] | " +
                        "//div[contains(@class, 'summary')] | //div[contains(@class, 'checkout')]"
                    )
                    if summary_indicators:
                        logger.info("✅ تم العثور على عناصر صفحة الملخص!")
                        self.test_results['reached_summary_page'] = True
                        return True
                except Exception as e:
                    logger.debug(f"خطأ في البحث عن عناصر الملخص: {e}")

                # إذا لم نصل بعد، انتظار أكثر
                if attempt < max_wait_attempts - 1:
                    logger.info(f"⏳ انتظار المزيد للوصول لصفحة الملخص...")

            final_url = self.driver.current_url
            logger.warning(f"⚠️ لم يتم الوصول لصفحة الملخص بعد {max_wait_attempts} محاولات")
            logger.info(f"الصفحة النهائية: {final_url}")
            return False
                
        except Exception as e:
            logger.error(f"❌ خطأ في الانتقال لصفحة الملخص: {e}")
            return False
    
    def click_continue_to_payment(self):
        """النقر على زر Continue to payment"""
        logger.info("💰 البحث عن زر 'Continue to payment' والنقر عليه...")
        
        try:
            # انتظار تحميل الصفحة
            time.sleep(3)
            
            # البحث عن زر Continue to payment بطرق متعددة
            continue_payment_selectors = [
                "button:contains('Continue to payment')",  # سيتم تجاهله
                "//button[contains(text(), 'Continue to payment')]",  # XPath
                "//button[contains(text(), 'Continue')]",  # XPath عام
                "button[class*='payment']",
                ".payment-btn",
                "button[style*='background']",  # الزر الأخضر
                "button[style*='green']"
            ]
            
            continue_button = None
            
            # محاولة XPath أولاً
            try:
                continue_button = self.driver.find_element(By.XPATH, "//button[contains(text(), 'Continue to payment')]")
                if continue_button.is_displayed() and continue_button.is_enabled():
                    logger.info("✅ تم العثور على زر 'Continue to payment' باستخدام XPath")
                else:
                    continue_button = None
            except:
                pass
            
            # إذا لم نجد الزر، نبحث بطرق أخرى
            if not continue_button:
                try:
                    continue_button = self.driver.find_element(By.XPATH, "//button[contains(text(), 'Continue')]")
                    if continue_button.is_displayed() and continue_button.is_enabled():
                        logger.info("✅ تم العثور على زر 'Continue' باستخدام XPath")
                    else:
                        continue_button = None
                except:
                    pass
            
            # البحث في جميع الأزرار
            if not continue_button:
                try:
                    all_buttons = self.driver.find_elements(By.TAG_NAME, "button")
                    for button in all_buttons:
                        if button.is_displayed() and button.is_enabled():
                            button_text = button.text.lower()
                            if 'continue' in button_text and 'payment' in button_text:
                                continue_button = button
                                logger.info("✅ تم العثور على زر 'Continue to payment' في قائمة الأزرار")
                                break
                            elif 'continue' in button_text:
                                continue_button = button
                                logger.info("✅ تم العثور على زر 'Continue' في قائمة الأزرار")
                                break
                except:
                    pass
            
            if not continue_button:
                logger.error("❌ لم يتم العثور على زر 'Continue to payment'")
                return False
            
            # حفظ URL الحالي للمقارنة
            current_url = self.driver.current_url
            logger.info(f"URL قبل النقر: {current_url}")
            
            # التمرير للزر
            self.driver.execute_script("arguments[0].scrollIntoView(true);", continue_button)
            time.sleep(1)
            
            # محاولة النقر على الزر
            logger.info(f"🔘 محاولة النقر على زر: {continue_button.text}")
            
            try:
                continue_button.click()
                self.test_results['clicked_continue_payment'] = True
                logger.info("✅ تم النقر على زر 'Continue to payment'")
            except ElementClickInterceptedException:
                # محاولة النقر باستخدام JavaScript
                logger.info("🔄 محاولة النقر باستخدام JavaScript...")
                self.driver.execute_script("arguments[0].click();", continue_button)
                self.test_results['clicked_continue_payment'] = True
                logger.info("✅ تم النقر على زر 'Continue to payment' باستخدام JavaScript")
            
            # انتظار الانتقال
            logger.info("⏳ انتظار الانتقال لصفحة الدفع...")
            time.sleep(5)
            
            # فحص URL الجديد
            new_url = self.driver.current_url
            logger.info(f"URL بعد النقر: {new_url}")
            
            if "payment" in new_url.lower():
                logger.info("✅ تم الانتقال لصفحة الدفع بنجاح!")
                self.test_results['reached_payment_page'] = True
                return True
            else:
                logger.warning("⚠️ لم يتم الانتقال لصفحة الدفع")
                return False
            
        except Exception as e:
            logger.error(f"❌ خطأ في النقر على زر Continue to payment: {e}")
            return False
    
    def switch_to_payment_iframe(self):
        """التبديل إلى iframe الدفع مع معالجة محسنة"""
        logger.info("🔄 البحث عن iframe الدفع والتبديل إليه...")
        
        try:
            # التأكد من العودة للمحتوى الرئيسي أولاً
            try:
                self.driver.switch_to.default_content()
                logger.info("✅ تم العودة للمحتوى الرئيسي")
            except:
                logger.debug("لم يتم العثور على محتوى رئيسي")

            # انتظار تحميل الصفحة بشكل كامل
            WebDriverWait(self.driver, 10).until(
                lambda driver: driver.execute_script("return document.readyState") == "complete"
            )
            
            # البحث عن iframe بطرق متعددة مع محاولات متكررة
            iframe_selectors = [
                # محددات خاصة بـ Ding.com (بناءً على iframe المرفق)
                "iframe[title='payment-frame']",
                "iframe[name='paymentFrame']",
                "iframe[src*='pp2.ding.com']",
                "iframe[src*='paymentforms']",
                "iframe[src*='newcardform']",
                "iframe[src*='orderRef']",
                "iframe[src*='paymentType=Visa']",

                # محددات عامة
                "iframe[title*='payment']",
                "iframe[name*='payment']",
                "iframe[id*='payment']",
                "iframe[src*='payment']",

                # محددات إضافية
                "iframe[src*='card']",
                "iframe[title*='card']",
                "iframe[name*='card']"
            ]
            
            max_attempts = 3
            attempt = 0
            iframe_found = False
            
            while attempt < max_attempts and not iframe_found:
                attempt += 1
                logger.info(f"محاولة العثور على iframe (محاولة {attempt}/{max_attempts})")
                
                # محاولة العثور على iframe باستخدام المحددات
                for selector in iframe_selectors:
                    try:
                        # انتظار ظهور iframe
                        iframe = WebDriverWait(self.driver, 5).until(
                            EC.presence_of_element_located((By.CSS_SELECTOR, selector))
                        )
                        
                        if not iframe.is_displayed():
                            continue
                        
                        # التحقق من خصائص iframe
                        src = iframe.get_attribute('src') or ''
                        title = iframe.get_attribute('title') or ''
                        name = iframe.get_attribute('name') or ''
                        
                        logger.info(f"تم العثور على iframe محتمل:")
                        logger.info(f"  Selector: {selector}")
                        logger.info(f"  Title: {title}")
                        logger.info(f"  Source: {src[:100]}...")
                        
                        # محاولة التبديل إلى iframe
                        try:
                            # التمرير إلى iframe
                            self.driver.execute_script("arguments[0].scrollIntoView({behavior: 'smooth', block: 'center'});", iframe)
                            time.sleep(1)
                            
                            # التبديل إلى iframe
                            self.driver.switch_to.frame(iframe)
                            
                            # التحقق من نجاح التبديل
                            try:
                                # محاولة العثور على عنصر داخل iframe
                                WebDriverWait(self.driver, 5).until(
                                    EC.presence_of_element_located((By.CSS_SELECTOR, "input[type='text'], input[type='tel'], input[type='number']"))
                                )
                                logger.info("✅ تم التحقق من محتوى iframe")
                                iframe_found = True
                                break
                            except:
                                # العودة للمحتوى الرئيسي إذا لم نجد عناصر الدفع
                                self.driver.switch_to.default_content()
                                continue
                            
                        except Exception as switch_error:
                            logger.debug(f"فشل التبديل إلى iframe: {switch_error}")
                            # العودة للمحتوى الرئيسي في حالة الفشل
                            self.driver.switch_to.default_content()
                            continue
                            
                    except Exception as e:
                        continue
                
                if not iframe_found:
                    # البحث في جميع iframes إذا لم نجد بالمحددات
                    try:
                        iframes = self.driver.find_elements(By.TAG_NAME, "iframe")
                        logger.info(f"🔍 تم العثور على {len(iframes)} iframe، جاري فحصها...")
                        
                        for i, iframe in enumerate(iframes):
                            try:
                                if iframe.is_displayed():
                                    src = iframe.get_attribute('src') or ''
                                    title = iframe.get_attribute('title') or ''
                                    name = iframe.get_attribute('name') or ''
                                    
                                    # فحص إذا كان iframe متعلق بالدفع
                                    payment_indicators = ['payment', 'card', 'form', 'pp2.ding.com']
                                    if any(indicator in src.lower() or indicator in title.lower() or indicator in name.lower() 
                                           for indicator in payment_indicators):
                                        
                                        logger.info(f"محاولة التبديل إلى iframe رقم {i+1}")
                                        
                                        try:
                                            self.driver.switch_to.frame(iframe)
                                            # التحقق من وجود عناصر الدفع
                                            WebDriverWait(self.driver, 5).until(
                                                EC.presence_of_element_located((By.CSS_SELECTOR, "input[type='text'], input[type='tel'], input[type='number']"))
                                            )
                                            iframe_found = True
                                            logger.info(f"✅ تم التبديل بنجاح إلى iframe رقم {i+1}")
                                            break
                                        except:
                                            self.driver.switch_to.default_content()
                                            continue
                            except:
                                continue
                                
                    except Exception as e:
                        logger.error(f"خطأ في البحث عن iframes: {e}")
                
                if not iframe_found and attempt < max_attempts:
                    logger.info(f"⏳ انتظار قبل المحاولة التالية ({attempt}/{max_attempts})...")
                    time.sleep(3)
            
            if iframe_found:
                logger.info("✅ تم التبديل إلى iframe الدفع بنجاح")
                # انتظار إضافي للتأكد من تحميل المحتوى
                time.sleep(2)
            else:
                logger.warning("⚠️ لم يتم العثور على iframe الدفع المناسب")
            
            return iframe_found
            
        except Exception as e:
            logger.error(f"❌ خطأ في التبديل إلى iframe الدفع: {e}")
            return False

    def find_payment_fields(self):
        """العثور على حقول الدفع مع معالجة محسنة"""
        logger.info("🔍 البحث عن حقول الدفع...")
        
        fields = {
            'card_number': None,
            'expiry': None,
            'cvv': None,
            'name_on_card': None,
            'zip_code': None
        }
        
        # أولاً: محاولة التبديل إلى iframe الدفع
        iframe_switched = self.switch_to_payment_iframe()
        
        if not iframe_switched:
            logger.warning("⚠️ لم يتم العثور على iframe الدفع، سيتم البحث في الصفحة الرئيسية")
        
        # انتظار تحميل النموذج مع التحقق من الجاهزية
        max_wait_attempts = 10
        form_ready = False
        
        for attempt in range(max_wait_attempts):
            try:
                # البحث عن أي حقل إدخال للتأكد من تحميل النموذج
                input_elements = self.driver.find_elements(By.CSS_SELECTOR, "input[type='text'], input[type='tel'], input[type='number'], input[type='password']")
                if input_elements and any(elem.is_displayed() for elem in input_elements):
                    form_ready = True
                    logger.info(f"✅ تم تحميل النموذج بعد {attempt + 1} محاولة")
                    break
                else:
                    time.sleep(1)
            except:
                time.sleep(1)
        
        if not form_ready:
            logger.warning("⚠️ لم يتم التأكد من تحميل النموذج بالكامل")
        
        # انتظار إضافي للتأكد
        time.sleep(2)

        # البحث عن الحقول باستخدام النصوص المحيطة (labels) أولاً
        logger.info("🏷️ البحث عن الحقول باستخدام النصوص المحيطة...")
        try:
            # الحصول على المستند الحالي (iframe أو الصفحة الرئيسية)
            current_doc = self.driver

            # البحث عن جميع العناصر النصية
            all_text_elements = current_doc.find_elements(By.XPATH, "//*[text()]")

            for text_element in all_text_elements:
                try:
                    text_content = text_element.text.strip().lower()
                    if not text_content:
                        continue

                    # البحث عن حقل الإدخال القريب من النص
                    parent = text_element.find_element(By.XPATH, "./..")
                    nearby_inputs = parent.find_elements(By.TAG_NAME, "input")

                    for input_elem in nearby_inputs:
                        if not input_elem.is_displayed():
                            continue

                        input_type = input_elem.get_attribute('type') or 'text'
                        if input_type.lower() not in ['text', 'tel', 'number', 'email']:
                            continue

                        # تحديد نوع الحقل بناءً على النص المحيط
                        if 'card number' in text_content and not fields['card_number']:
                            fields['card_number'] = input_elem
                            logger.info(f"✅ تم العثور على حقل رقم البطاقة بواسطة النص: '{text_content}'")
                        elif 'expiry' in text_content and not fields['expiry']:
                            fields['expiry'] = input_elem
                            logger.info(f"✅ تم العثور على حقل تاريخ الانتهاء بواسطة النص: '{text_content}'")
                        elif 'cvv' in text_content and not fields['cvv']:
                            fields['cvv'] = input_elem
                            logger.info(f"✅ تم العثور على حقل CVV بواسطة النص: '{text_content}'")
                        elif 'name on card' in text_content and not fields['name_on_card']:
                            fields['name_on_card'] = input_elem
                            logger.info(f"✅ تم العثور على حقل الاسم بواسطة النص: '{text_content}'")
                        elif ('country' in text_content or 'zip' in text_content or 'postal' in text_content) and not fields['zip_code']:
                            fields['zip_code'] = input_elem
                            logger.info(f"✅ تم العثور على حقل الرمز البريدي بواسطة النص: '{text_content}'")

                except Exception:
                    continue

        except Exception as e:
            logger.debug(f"خطأ في البحث بالنصوص المحيطة: {e}")

        # البحث عن حقول الدفع بطرق متعددة (إذا لم نجد بالنصوص المحيطة)
        try:
            # رقم البطاقة (بناءً على النموذج المرفق)
            card_selectors = [
                # محددات خاصة بالنموذج المرفق
                "input[type='text']",  # الحقل الأول في النموذج
                "input[placeholder*='Card number']",
                "input[placeholder*='card number']",
                "input[placeholder*='Card Number']",

                # محددات عامة
                "input[name*='card']",
                "input[id*='card']",
                "input[autocomplete='cc-number']",
                "input[data-testid*='card']",
                "input[type='text'][maxlength='19']",  # عادة رقم البطاقة
                "input[type='tel'][maxlength='19']",
                "input[type='text'][maxlength='23']",  # مع المسافات
                "input[class*='card']",
                "input[id*='cardNumber']",
                "input[name*='cardNumber']"
            ]
            
            for selector in card_selectors:
                try:
                    element = self.driver.find_element(By.CSS_SELECTOR, selector)
                    if element.is_displayed():
                        fields['card_number'] = element
                        logger.info("✅ تم العثور على حقل رقم البطاقة")
                        break
                except:
                    continue
            
            # تاريخ الانتهاء (الحقل الثاني في النموذج)
            expiry_selectors = [
                # محددات خاصة بالنموذج المرفق - الحقل تحت "Expiry"
                "input[type='text']:nth-of-type(2)",  # الحقل الثاني
                "input[placeholder*='Expiry']",
                "input[placeholder*='MM/YY']",
                "input[placeholder*='expiry']",
                "input[placeholder*='Expiry Date']",
                "input[name*='expiry']",
                "input[id*='expiry']",
                "input[autocomplete='cc-exp']",
                "input[placeholder*='MM/YYYY']",
                "input[maxlength='5']",  # عادة MM/YY
                "input[maxlength='7']",  # MM/YYYY
                "input[class*='expiry']",
                "input[id*='expiryDate']",
                "input[name*='expiryDate']"
            ]
            
            for selector in expiry_selectors:
                try:
                    element = self.driver.find_element(By.CSS_SELECTOR, selector)
                    if element.is_displayed():
                        fields['expiry'] = element
                        logger.info("✅ تم العثور على حقل تاريخ الانتهاء")
                        break
                except:
                    continue
            
            # CVV (الحقل الثالث في النموذج - بجانب Expiry)
            cvv_selectors = [
                # محددات خاصة بالنموذج المرفق - الحقل تحت "CVV"
                "input[type='text']:nth-of-type(3)",  # الحقل الثالث
                "input[placeholder*='CVV']",
                "input[placeholder*='CVC']",
                "input[placeholder*='cvv']",
                "input[placeholder*='Security code']",
                "input[name*='cvv']",
                "input[id*='cvv']",
                "input[autocomplete='cc-csc']",
                "input[maxlength='3']",  # عادة CVV
                "input[maxlength='4']",   # أو 4 أرقام
                "input[class*='cvv']",
                "input[class*='cvc']",
                "input[id*='securityCode']",
                "input[name*='securityCode']"
            ]
            
            for selector in cvv_selectors:
                try:
                    element = self.driver.find_element(By.CSS_SELECTOR, selector)
                    if element.is_displayed():
                        # التأكد أنه ليس حقل تاريخ الانتهاء
                        placeholder = element.get_attribute('placeholder') or ''
                        if 'mm' not in placeholder.lower() and 'yy' not in placeholder.lower():
                            fields['cvv'] = element
                            logger.info("✅ تم العثور على حقل CVV")
                            break
                except:
                    continue
            
            # الاسم على البطاقة (الحقل الرابع في النموذج)
            name_selectors = [
                # محددات خاصة بالنموذج المرفق - الحقل تحت "Name on card"
                "input[type='text']:nth-of-type(4)",  # الحقل الرابع
                "input[placeholder*='Name on card']",
                "input[placeholder*='name']",
                "input[placeholder*='Cardholder name']",
                "input[name*='name']",
                "input[id*='name']",
                "input[autocomplete='cc-name']",
                "input[class*='name']",
                "input[id*='cardholderName']",
                "input[name*='cardholderName']"
            ]
            
            for selector in name_selectors:
                try:
                    element = self.driver.find_element(By.CSS_SELECTOR, selector)
                    if element.is_displayed():
                        placeholder = element.get_attribute('placeholder') or ''
                        if 'card' in placeholder.lower() or 'name' in placeholder.lower() or 'holder' in placeholder.lower():
                            fields['name_on_card'] = element
                            logger.info("✅ تم العثور على حقل الاسم")
                            break
                except:
                    continue
            
            # الرمز البريدي
            zip_selectors = [
                "input[placeholder*='ZIP']",
                "input[placeholder*='Postal']",
                "input[placeholder*='zip']",
                "input[placeholder*='Postal code']",
                "input[name*='zip']",
                "input[id*='zip']",
                "input[autocomplete='postal-code']",
                "input[class*='zip']",
                "input[class*='postal']",
                "input[id*='postalCode']",
                "input[name*='postalCode']"
            ]
            
            for selector in zip_selectors:
                try:
                    element = self.driver.find_element(By.CSS_SELECTOR, selector)
                    if element.is_displayed():
                        fields['zip_code'] = element
                        logger.info("✅ تم العثور على حقل الرمز البريدي")
                        break
                except:
                    continue
            
            # إذا لم نجد أي حقول، نبحث في جميع input elements
            if not fields:
                logger.info("🔍 البحث في جميع حقول الإدخال...")
                all_inputs = self.driver.find_elements(By.TAG_NAME, "input")

                # فلترة الحقول المرئية من نوع text
                visible_text_inputs = []
                for input_elem in all_inputs:
                    try:
                        if input_elem.is_displayed() and input_elem.get_attribute('type') in ['text', 'tel', 'number']:
                            visible_text_inputs.append(input_elem)
                    except:
                        continue

                logger.info(f"تم العثور على {len(visible_text_inputs)} حقل إدخال مرئي")

                # ترتيب الحقول حسب الموقع (من أعلى لأسفل)
                def get_position(element):
                    try:
                        location = element.location
                        return (location['y'], location['x'])
                    except:
                        return (9999, 9999)

                visible_text_inputs.sort(key=get_position)

                # طباعة معلومات الحقول للتشخيص
                logger.info("📋 تحليل الحقول المكتشفة:")
                for i, input_elem in enumerate(visible_text_inputs):
                    try:
                        placeholder = input_elem.get_attribute('placeholder') or ''
                        name = input_elem.get_attribute('name') or ''
                        id_attr = input_elem.get_attribute('id') or ''
                        location = input_elem.location
                        current_value = input_elem.get_attribute('value') or ''
                        logger.info(f"  حقل {i+1}: placeholder='{placeholder}', name='{name}', id='{id_attr}', موقع=({location['x']}, {location['y']}), قيمة='{current_value}'")
                    except:
                        logger.info(f"  حقل {i+1}: خطأ في قراءة المعلومات")

                # تعيين الحقول بناءً على الترتيب مع التحقق من المحتوى
                if len(visible_text_inputs) >= 4:
                    # الحقل الأول: رقم البطاقة (يجب أن يكون فارغ أو يحتوي على أرقام)
                    if not fields.get('card_number'):
                        first_field = visible_text_inputs[0]
                        current_value = first_field.get_attribute('value') or ''
                        # التحقق من أن الحقل مناسب لرقم البطاقة
                        if not current_value or current_value.replace(' ', '').isdigit():
                            fields['card_number'] = first_field
                            logger.info("✅ تم تحديد حقل رقم البطاقة (الحقل الأول)")

                    # الحقل الثاني: تاريخ الانتهاء
                    if not fields.get('expiry') and len(visible_text_inputs) > 1:
                        fields['expiry'] = visible_text_inputs[1]
                        logger.info("✅ تم تحديد حقل تاريخ الانتهاء (الحقل الثاني)")

                    # الحقل الثالث: CVV
                    if not fields.get('cvv') and len(visible_text_inputs) > 2:
                        fields['cvv'] = visible_text_inputs[2]
                        logger.info("✅ تم تحديد حقل CVV (الحقل الثالث)")

                    # الحقل الرابع: الاسم على البطاقة (يجب أن يكون فارغ أو يحتوي على نص)
                    if not fields.get('name_on_card') and len(visible_text_inputs) > 3:
                        fourth_field = visible_text_inputs[3]
                        current_value = fourth_field.get_attribute('value') or ''
                        # التحقق من أن الحقل لا يحتوي على رقم بطاقة
                        if not current_value or not current_value.replace(' ', '').isdigit() or len(current_value.replace(' ', '')) < 10:
                            fields['name_on_card'] = fourth_field
                            logger.info("✅ تم تحديد حقل الاسم (الحقل الرابع)")
                        else:
                            logger.warning(f"⚠️ الحقل الرابع يحتوي على أرقام مشبوهة: '{current_value}' - قد يكون هناك خطأ في التعيين")

                # البحث التقليدي بناءً على النص والخصائص
                for i, input_elem in enumerate(visible_text_inputs):
                    try:
                        placeholder = input_elem.get_attribute('placeholder') or ''
                        name = input_elem.get_attribute('name') or ''
                        id_attr = input_elem.get_attribute('id') or ''
                        input_type = input_elem.get_attribute('type') or ''

                        logger.info(f"  Input {i+1}: type={input_type}, placeholder='{placeholder}', name='{name}', id='{id_attr}'")

                        # تصنيف الحقول بناءً على الخصائص (إذا لم يتم تعيينها بالترتيب)
                        if any(keyword in placeholder.lower() for keyword in ['card', 'number']) and 'card_number' not in fields:
                            fields['card_number'] = input_elem
                            logger.info(f"✅ تم تحديد حقل رقم البطاقة بالنص: {placeholder}")
                        elif any(keyword in placeholder.lower() for keyword in ['expiry', 'mm/yy', 'date']) and 'expiry' not in fields:
                            fields['expiry'] = input_elem
                            logger.info(f"✅ تم تحديد حقل تاريخ الانتهاء بالنص: {placeholder}")
                        elif any(keyword in placeholder.lower() for keyword in ['cvv', 'cvc', 'security']) and 'cvv' not in fields:
                            fields['cvv'] = input_elem
                            logger.info(f"✅ تم تحديد حقل CVV بالنص: {placeholder}")
                        elif any(keyword in placeholder.lower() for keyword in ['name', 'holder']) and 'name_on_card' not in fields:
                            fields['name_on_card'] = input_elem
                            logger.info(f"✅ تم تحديد حقل الاسم بالنص: {placeholder}")
                        elif any(keyword in placeholder.lower() for keyword in ['zip', 'postal']) and 'zip_code' not in fields:
                            fields['zip_code'] = input_elem
                            logger.info(f"✅ تم تحديد حقل الرمز البريدي بالنص: {placeholder}")
                    except:
                        continue
            
        except Exception as e:
            logger.error(f"❌ خطأ في البحث عن الحقول: {e}")
        
        # طباعة ملخص الحقول المكتشفة مع التفاصيل
        found_fields = [key for key, value in fields.items() if value is not None]
        logger.info(f"📊 تم العثور على {len(found_fields)} حقل: {found_fields}")

        # طباعة تفاصيل كل حقل مكتشف
        for field_name, field_element in fields.items():
            if field_element:
                try:
                    placeholder = field_element.get_attribute('placeholder') or ''
                    name = field_element.get_attribute('name') or ''
                    id_attr = field_element.get_attribute('id') or ''
                    current_value = field_element.get_attribute('value') or ''
                    location = field_element.location

                    logger.info(f"  🔍 {field_name}: placeholder='{placeholder}', name='{name}', id='{id_attr}', قيمة='{current_value}', موقع=({location['x']}, {location['y']})")
                except Exception as e:
                    logger.info(f"  🔍 {field_name}: خطأ في قراءة التفاصيل - {e}")

        # تحذير إذا لم نجد الحقول الأساسية
        essential_fields = ['card_number', 'expiry', 'cvv']
        missing_fields = [field for field in essential_fields if not fields.get(field)]
        if missing_fields:
            logger.warning(f"⚠️ لم يتم العثور على الحقول الأساسية: {missing_fields}")

        return fields

    def find_specific_field(self, field_type):
        """البحث عن حقل محدد بدقة عالية"""
        try:
            # البحث عن جميع حقول الإدخال
            input_fields = self.driver.find_elements(By.CSS_SELECTOR, "input")

            for field in input_fields:
                # جمع معلومات الحقل
                field_type_attr = field.get_attribute('type') or 'text'
                placeholder = (field.get_attribute('placeholder') or '').lower()
                name = (field.get_attribute('name') or '').lower()
                id_attr = (field.get_attribute('id') or '').lower()
                aria_label = (field.get_attribute('aria-label') or '').lower()

                # تحديد نوع الحقل بدقة
                if field_type == 'card_number':
                    if (('card' in placeholder and 'number' in placeholder) or
                        ('card' in name and 'number' in name) or
                        ('cardnumber' in name) or
                        ('card-number' in id_attr) or
                        ('cardNumber' in id_attr)):
                        logger.info(f"✅ تم العثور على حقل رقم البطاقة: {placeholder}")
                        return field

                elif field_type == 'expiry':
                    if (('expir' in placeholder) or ('date' in placeholder) or
                        ('expir' in name) or ('date' in name) or
                        ('mm/yy' in placeholder) or ('mm/yyyy' in placeholder)):
                        logger.info(f"✅ تم العثور على حقل تاريخ الانتهاء: {placeholder}")
                        return field

                elif field_type == 'cvv':
                    if (('cvv' in placeholder) or ('cvc' in placeholder) or ('security' in placeholder) or
                        ('cvv' in name) or ('cvc' in name) or ('security' in name)):
                        logger.info(f"✅ تم العثور على حقل CVV: {placeholder}")
                        return field

                elif field_type == 'cardholder_name':
                    if (('name' in placeholder and 'card' in placeholder) or
                        ('cardholder' in placeholder) or ('holder' in placeholder) or
                        ('name' in name and 'card' in name) or
                        ('cardholder' in name) or ('holder' in name) or
                        ('name on card' in placeholder.lower()) or
                        ('card holder' in placeholder.lower()) or
                        ('name' in placeholder and len(placeholder.split()) <= 3)):  # حقل اسم بسيط
                        logger.info(f"✅ تم العثور على حقل اسم حامل البطاقة: {placeholder}")
                        return field

            logger.warning(f"⚠️ لم يتم العثور على حقل {field_type}")
            return None

        except Exception as e:
            logger.error(f"❌ خطأ في البحث عن حقل {field_type}: {e}")
            return None

    def fill_card_number_safely(self, element, value):
        """ملء رقم البطاقة بطريقة آمنة وبطيئة"""
        try:
            logger.info("💳 بدء ملء رقم البطاقة بطريقة آمنة...")

            # التأكد من التركيز على الحقل
            self.driver.execute_script("arguments[0].focus();", element)
            time.sleep(0.5)

            # مسح أي قيمة موجودة
            element.clear()
            self.driver.execute_script("arguments[0].value = '';", element)
            time.sleep(0.3)

            # كتابة رقم البطاقة حرف بحرف
            for i, char in enumerate(value):
                element.send_keys(char)
                time.sleep(0.1)  # تأخير بين كل رقم

                # فحص دوري للتأكد من عدم المسح
                if i % 4 == 3:  # كل 4 أرقام
                    current_value = element.get_attribute('value') or ''
                    expected_so_far = value[:i+1]
                    if not current_value.replace(' ', '').replace('-', '').startswith(expected_so_far):
                        logger.warning(f"⚠️ تم مسح جزء من رقم البطاقة، إعادة المحاولة...")
                        element.clear()
                        self.driver.execute_script("arguments[0].value = '';", element)
                        time.sleep(0.2)
                        element.send_keys(value[:i+1])
                        time.sleep(0.3)

            # التحقق النهائي
            final_value = element.get_attribute('value') or ''
            clean_final = final_value.replace(' ', '').replace('-', '')
            if clean_final == value:
                logger.info("✅ تم ملء رقم البطاقة بنجاح")
                return True
            else:
                logger.warning(f"⚠️ رقم البطاقة غير مطابق. المتوقع: {value}, الموجود: {clean_final}")
                return False

        except Exception as e:
            logger.error(f"❌ خطأ في ملء رقم البطاقة: {e}")
            return False

    def fill_field_safely(self, element, value, field_name):
        """ملء حقل بطريقة آمنة مع التحقق"""
        try:
            logger.info(f"📝 ملء حقل {field_name}: {value}")

            # التأكد من التركيز
            self.driver.execute_script("arguments[0].focus();", element)
            time.sleep(0.3)

            # مسح القيمة الحالية
            element.clear()
            self.driver.execute_script("arguments[0].value = '';", element)
            time.sleep(0.2)

            # كتابة القيمة الجديدة
            element.send_keys(value)
            time.sleep(0.5)

            # التحقق من النجاح
            final_value = element.get_attribute('value') or ''
            if final_value == value:
                logger.info(f"✅ تم ملء {field_name} بنجاح")
                return True
            else:
                logger.warning(f"⚠️ {field_name} غير مطابق. المتوقع: {value}, الموجود: {final_value}")
                return False

        except Exception as e:
            logger.error(f"❌ خطأ في ملء {field_name}: {e}")
            return False

    def verify_all_fields_filled(self):
        """التحقق من ملء جميع الحقول بشكل صحيح"""
        try:
            logger.info("🔍 التحقق من ملء جميع الحقول...")

            verification_results = {}

            # فحص رقم البطاقة
            card_field = self.find_specific_field('card_number')
            if card_field:
                card_value = (card_field.get_attribute('value') or '').replace(' ', '').replace('-', '')
                verification_results['card_number'] = card_value == self.test_card_data['card_number']
                logger.info(f"💳 رقم البطاقة: {'✅' if verification_results['card_number'] else '❌'}")

            # فحص تاريخ الانتهاء
            expiry_field = self.find_specific_field('expiry')
            if expiry_field:
                expiry_value = expiry_field.get_attribute('value') or ''
                verification_results['expiry'] = expiry_value == self.test_card_data['expiry_full']
                logger.info(f"📅 تاريخ الانتهاء: {'✅' if verification_results['expiry'] else '❌'}")

            # فحص CVV
            cvv_field = self.find_specific_field('cvv')
            if cvv_field:
                cvv_value = cvv_field.get_attribute('value') or ''
                verification_results['cvv'] = cvv_value == self.test_card_data['cvv']
                logger.info(f"🔐 CVV: {'✅' if verification_results['cvv'] else '❌'}")

            # فحص اسم حامل البطاقة
            name_field = self.find_specific_field('cardholder_name')
            if name_field:
                name_value = name_field.get_attribute('value') or ''
                verification_results['cardholder_name'] = name_value == self.test_card_data['name_on_card']
                logger.info(f"👤 اسم حامل البطاقة: {'✅' if verification_results['cardholder_name'] else '❌'}")

            success_count = sum(verification_results.values())
            total_count = len(verification_results)

            logger.info(f"📊 النتيجة النهائية: {success_count}/{total_count} حقول صحيحة")

            return success_count >= 3  # نحتاج على الأقل 3 حقول صحيحة

        except Exception as e:
            logger.error(f"❌ خطأ في التحقق من الحقول: {e}")
            return False

    def clear_and_fill_phone_field(self, element, value):
        """مسح وملء حقل رقم الهاتف بطريقة مبسطة ومستقرة"""
        logger.info(f"📱 مسح وملء حقل رقم الهاتف: {value}")

        try:
            # التأكد من أن العنصر ما زال صالحاً
            if not element.is_displayed() or not element.is_enabled():
                logger.warning("⚠️ حقل الهاتف غير متاح")
                return False

            # التمرير للعنصر وإزالة العناصر المتداخلة
            self.driver.execute_script("arguments[0].scrollIntoView({block: 'center'});", element)
            time.sleep(0.5)

            # إزالة أي عناصر قد تحجب الحقل
            try:
                self.driver.execute_script("""
                    // إخفاء القوائم المنسدلة التي قد تحجب الحقل
                    var menus = document.querySelectorAll('[data-testid="company-menu-wrap"]');
                    menus.forEach(function(menu) {
                        menu.style.display = 'none';
                        menu.style.visibility = 'hidden';
                        menu.style.pointerEvents = 'none';
                    });

                    // التأكد من أن الحقل قابل للنقر
                    var element = arguments[0];
                    element.style.zIndex = '9999';
                    element.style.position = 'relative';
                """, element)
                time.sleep(0.3)
            except:
                logger.debug("تعذر إزالة العناصر المتداخلة")

            # التحقق من القيمة الحالية
            current_value = element.get_attribute('value')
            logger.info(f"📱 القيمة الحالية في الحقل: '{current_value}'")

            # إذا كانت القيمة صحيحة بالفعل، لا نحتاج لفعل شيء
            if current_value == value:
                logger.info("✅ الرقم صحيح بالفعل، لا حاجة للتغيير")
                return True

            # طريقة مبسطة ومستقرة: مسح شامل ثم ملء واحد
            logger.info("🧹 بدء عملية المسح والملء المبسطة...")

            # الخطوة 1: مسح شامل بـ JavaScript (الأكثر موثوقية)
            self.driver.execute_script("""
                arguments[0].focus();
                arguments[0].value = '';
                arguments[0].setAttribute('value', '');
            """, element)
            time.sleep(0.5)

            # الخطوة 2: التحقق من المسح
            current_value = element.get_attribute('value') or ''
            if current_value.strip():
                logger.info(f"🔄 مسح إضافي للقيمة المتبقية: '{current_value}'")
                # مسح إضافي بـ Ctrl+A + Delete
                element.send_keys(Keys.CONTROL + "a")
                time.sleep(0.2)
                element.send_keys(Keys.DELETE)
                time.sleep(0.3)

            # الخطوة 3: ملء القيمة الجديدة
            logger.info(f"📝 ملء القيمة الجديدة: {value}")
            element.send_keys(value)
            time.sleep(0.8)  # انتظار أطول للتأكد من الاستقرار

            # الخطوة 4: التحقق النهائي
            final_value = element.get_attribute('value') or ''
            if final_value == value:
                logger.info(f"✅ تم ملء الرقم بنجاح: {final_value}")

                # إطلاق أحداث التغيير
                self.driver.execute_script("""
                    arguments[0].dispatchEvent(new Event('input', { bubbles: true }));
                    arguments[0].dispatchEvent(new Event('change', { bubbles: true }));
                    arguments[0].dispatchEvent(new Event('blur', { bubbles: true }));
                """, element)
                time.sleep(0.3)

                return True
            else:
                logger.warning(f"⚠️ القيمة النهائية غير صحيحة: '{final_value}' بدلاً من '{value}'")
                return False

        except Exception as e:
            logger.error(f"❌ خطأ في مسح وملء حقل الهاتف: {e}")
            return False

    def fill_field_with_multiple_methods(self, element, value, field_name):
        """ملء حقل باستخدام طرق متعددة ومحسنة"""
        logger.info(f"🧪 اختبار ملء {field_name}...")

        try:
            # التأكد من أن العنصر مرئي وقابل للتفاعل
            if not element.is_displayed() or not element.is_enabled():
                logger.warning(f"⚠️ العنصر غير مرئي أو غير قابل للتفاعل: {field_name}")
                return False

            # التحقق من القيمة الحالية ومسحها إذا لزم الأمر
            current_value = element.get_attribute('value') or ''
            if current_value:
                logger.info(f"🧹 مسح القيمة الحالية من {field_name}: '{current_value}'")

                # مسح شامل للحقل
                try:
                    # الطريقة الأولى: تحديد الكل ومسح
                    element.click()
                    element.send_keys(Keys.CONTROL + "a")
                    element.send_keys(Keys.DELETE)
                    time.sleep(0.2)

                    # الطريقة الثانية: مسح بـ JavaScript
                    self.driver.execute_script("arguments[0].value = '';", element)
                    time.sleep(0.2)

                    # التحقق من المسح
                    after_clear = element.get_attribute('value') or ''
                    if after_clear:
                        logger.warning(f"⚠️ لم يتم مسح الحقل بالكامل، متبقي: '{after_clear}'")
                    else:
                        logger.info(f"✅ تم مسح {field_name} بنجاح")

                except Exception as e:
                    logger.warning(f"⚠️ خطأ في مسح {field_name}: {e}")

            # معالجة خاصة لحقل CVV
            is_cvv_field = 'cvv' in field_name.lower() or 'cvc' in field_name.lower()
            if is_cvv_field:
                logger.info("🔐 تم اكتشاف حقل CVV - استخدام معالجة خاصة")
                # انتظار إضافي لحقل CVV
                time.sleep(1)

            # التمرير للعنصر بشكل محسن
            try:
                # التمرير مع هامش أمان
                self.driver.execute_script("""
                    arguments[0].scrollIntoView({
                        behavior: 'smooth',
                        block: 'center',
                        inline: 'center'
                    });
                """, element)
                time.sleep(1)
            except:
                logger.warning("⚠️ فشل التمرير المحسن، جاري المحاولة بالطريقة البسيطة")
                self.driver.execute_script("arguments[0].scrollIntoView(true);", element)
                time.sleep(1)

            # محاولة إزالة أي عناصر متداخلة
            try:
                self.driver.execute_script("""
                    var element = arguments[0];
                    var rect = element.getBoundingClientRect();
                    var elements = document.elementsFromPoint(rect.left + rect.width/2, rect.top + rect.height/2);
                    elements.forEach(function(el) {
                        if (el !== element && el.style.pointerEvents !== 'none') {
                            el.style.pointerEvents = 'none';
                        }
                    });
                """, element)
            except:
                logger.debug("تعذر إزالة العناصر المتداخلة")

            # الطريقة 1: محاولة النقر والكتابة المباشرة مع معالجة الأخطاء
            logger.info(f"  🔹 الطريقة 1 - النقر والكتابة المباشرة")
            try:
                # محاولة النقر باستخدام JavaScript أولاً
                self.driver.execute_script("arguments[0].click();", element)
                element.clear()

                # معالجة خاصة لحقل رقم البطاقة فقط - كتابة بطيئة
                if ('card' in field_name.lower() and 'number' in field_name.lower()) and 'phone' not in field_name.lower():
                    logger.info("💳 كتابة رقم البطاقة حرف بحرف...")
                    for char in value:
                        element.send_keys(char)
                        time.sleep(0.08)  # تأخير بسيط بين كل رقم
                else:
                    element.send_keys(value)

                time.sleep(0.5)

                # التحقق من النجاح مع مراعاة تنسيق رقم البطاقة فقط
                current_value = element.get_attribute('value') or ''
                if ('card' in field_name.lower() and 'number' in field_name.lower()) and 'phone' not in field_name.lower():
                    # للبطاقة فقط: إزالة المسافات للمقارنة
                    success = current_value.replace(' ', '').replace('-', '') == value.replace(' ', '').replace('-', '')
                else:
                    success = current_value == value

                if success:
                    logger.info(f"  ✅ نجحت الطريقة 1: {field_name}")
                    # تحقق إضافي لحقول CVV
                    if is_cvv_field:
                        time.sleep(0.5)
                        if element.get_attribute('value') == value:
                            return True
                        else:
                            logger.warning("⚠️ تم مسح CVV بعد الملء - جاري المحاولة مرة أخرى")
                    else:
                        return True
            except:
                logger.debug("فشلت الطريقة 1")

            # الطريقة 2: استخدام Actions API المحسن
            logger.info(f"  🔹 الطريقة 2 - Actions API المحسن")
            try:
                actions = ActionChains(self.driver)
                actions.move_to_element(element)
                actions.click()
                actions.pause(0.5)
                actions.key_down(Keys.CONTROL).send_keys('a').key_up(Keys.CONTROL)
                actions.send_keys(Keys.DELETE)

                # معالجة خاصة لحقل رقم البطاقة فقط - كتابة بطيئة
                if ('card' in field_name.lower() and 'number' in field_name.lower()) and 'phone' not in field_name.lower():
                    logger.info("💳 كتابة رقم البطاقة بـ Actions مع تأخير...")
                    for char in value:
                        actions.send_keys(char)
                        actions.pause(0.08)
                else:
                    actions.send_keys(value)

                actions.perform()
                time.sleep(0.5)

                # التحقق من النجاح مع مراعاة تنسيق رقم البطاقة فقط
                current_value = element.get_attribute('value') or ''
                if ('card' in field_name.lower() and 'number' in field_name.lower()) and 'phone' not in field_name.lower():
                    # للبطاقة فقط: إزالة المسافات للمقارنة
                    success = current_value.replace(' ', '').replace('-', '') == value.replace(' ', '').replace('-', '')
                else:
                    success = current_value == value

                if success:
                    logger.info(f"  ✅ نجحت الطريقة 2: {field_name}")
                    # تحقق إضافي لحقول CVV
                    if is_cvv_field:
                        time.sleep(0.5)
                        if element.get_attribute('value') == value:
                            return True
                        else:
                            logger.warning("⚠️ تم مسح CVV بعد الملء - جاري المحاولة مرة أخرى")
                    else:
                        return True
            except:
                logger.debug("فشلت الطريقة 2")

            # الطريقة 3: JavaScript المحسن مع معالجة الأحداث
            logger.info(f"  🔹 الطريقة 3 - JavaScript المحسن")
            try:
                script = """
                    var element = arguments[0];
                    var value = arguments[1];
                    
                    // تنظيف القيمة الحالية
                    element.value = '';
                    
                    // محاكاة النقر
                    element.dispatchEvent(new MouseEvent('click', {
                        view: window,
                        bubbles: true,
                        cancelable: true
                    }));
                    
                    // محاكاة التركيز
                    element.dispatchEvent(new FocusEvent('focus'));
                    
                    // تعيين القيمة
                    element.value = value;
                    
                    // إطلاق أحداث input و change
                    element.dispatchEvent(new Event('input', { bubbles: true }));
                    element.dispatchEvent(new Event('change', { bubbles: true }));
                    
                    // محاكاة الكتابة
                    for (let i = 0; i < value.length; i++) {
                        element.dispatchEvent(new KeyboardEvent('keydown', {
                            key: value[i],
                            code: 'Key' + value[i].toUpperCase(),
                            bubbles: true
                        }));
                        element.dispatchEvent(new KeyboardEvent('keypress', {
                            key: value[i],
                            code: 'Key' + value[i].toUpperCase(),
                            bubbles: true
                        }));
                        element.dispatchEvent(new KeyboardEvent('keyup', {
                            key: value[i],
                            code: 'Key' + value[i].toUpperCase(),
                            bubbles: true
                        }));
                    }
                    
                    // تأكيد التركيز
                    element.focus();
                """
                self.driver.execute_script(script, element, value)
                time.sleep(0.5)
                
                if element.get_attribute('value') == value:
                    logger.info(f"  ✅ نجحت الطريقة 3: {field_name}")
                    return True
            except:
                logger.debug("فشلت الطريقة 3")

            # الطريقة 4: الكتابة البطيئة مع التحقق
            logger.info(f"  🔹 الطريقة 4 - الكتابة البطيئة")
            try:
                # مسح القيمة الحالية
                element.clear()
                element.click()
                
                # كتابة كل حرف مع التحقق
                current_value = ""
                for char in value:
                    element.send_keys(char)
                    time.sleep(0.1)
                    current_value += char
                    
                    # التحقق من القيمة الحالية
                    actual_value = element.get_attribute('value')
                    if not actual_value.endswith(current_value):
                        # إعادة المحاولة إذا لم تتم الكتابة بشكل صحيح
                        element.clear()
                        element.send_keys(value)
                        break
                
                time.sleep(0.5)
                if element.get_attribute('value') == value:
                    logger.info(f"  ✅ نجحت الطريقة 4: {field_name}")
                    return True
            except:
                logger.debug("فشلت الطريقة 4")

            # الطريقة 5: محاولة أخيرة باستخدام execCommand
            logger.info(f"  🔹 الطريقة 5 - execCommand")
            try:
                script = """
                    var element = arguments[0];
                    var value = arguments[1];
                    
                    // محاولة استخدام execCommand
                    element.focus();
                    document.execCommand('selectAll', false, null);
                    document.execCommand('delete', false, null);
                    document.execCommand('insertText', false, value);
                    
                    // التأكد من تحديث القيمة
                    if (element.value !== value) {
                        element.value = value;
                    }
                """
                self.driver.execute_script(script, element, value)
                time.sleep(0.5)
                
                if element.get_attribute('value') == value:
                    logger.info(f"  ✅ نجحت الطريقة 5: {field_name}")
                    return True
            except:
                logger.debug("فشلت الطريقة 5")

            logger.warning(f"❌ فشلت جميع الطرق في ملء {field_name}")
            return False
            
        except Exception as e:
            logger.error(f"❌ خطأ في ملء {field_name}: {e}")
            return False
    
    def select_card_payment_method(self):
        """اختيار طريقة الدفع بالبطاقة"""
        logger.info("💳 البحث عن أيقونة طريقة الدفع بالبطاقة...")
        
        try:
            # انتظار تحميل الصفحة
            time.sleep(3)
            
            # قائمة محددات أيقونة البطاقة - محدثة للواجهة الجديدة
            card_method_selectors = [
                # محددات خاصة بالواجهة الجديدة (من HTML المرفق)
                "div[data-testid='Visa']",
                "div.newPaymentItem__Item-sc-kuidrs-1.dndelG",
                "div[data-testid='Visa'].newPaymentItem__Item-sc-kuidrs-1",
                
                # محددات عامة لأيقونة البطاقة
                "//div[contains(@class, 'card') and contains(@class, 'payment')]",
                "//button[contains(@class, 'card') and contains(@class, 'method')]",
                "//div[contains(@class, 'payment-method') and contains(text(), 'Card')]",
                "//button[contains(text(), 'Credit Card')]",
                "//button[contains(text(), 'Debit Card')]",
                "//div[contains(@class, 'card-option')]",
                
                # محددات خاصة بـ Ding.com
                "//div[@data-payment-method='card']",
                "//button[@data-method='card']",
                "//div[contains(@class, 'payment-option-card')]",
                "//div[contains(@id, 'card-payment')]",
                
                # محددات بناءً على النص
                "//div[contains(text(), 'Card') or contains(text(), 'card')]",
                "//button[contains(text(), 'Card') or contains(text(), 'card')]",
                "//span[contains(text(), 'Credit') or contains(text(), 'Debit')]",
                
                # محددات بناءً على الصور أو الأيقونات
                "//img[contains(@alt, 'card') or contains(@alt, 'Card')]",
                "//i[contains(@class, 'card')]",
                "//svg[contains(@class, 'card')]",
                
                # محددات عامة للطرق المختلفة
                ".payment-method-card",
                ".card-payment-option",
                ".payment-option[data-method='card']",
                "[data-testid*='card']",
                "[data-testid*='Visa']",
                "[data-cy*='card']",
                
                # محددات للواجهة الجديدة
                ".newPaymentItem__Item-sc-kuidrs-1",
                "div[class*='newPaymentItem__Item']",
                "div[class*='paymentItem']"
            ]
            
            card_method_found = False
            
            # أولاً: البحث عن العنصر المحدد من HTML المرفق
            logger.info("🎯 البحث عن عنصر البطاقة المحدد...")
            specific_selectors = [
                "div[data-testid='Visa']",
                "div.newPaymentItem__Item-sc-kuidrs-1.dndelG",
                "div[data-testid='Visa'].newPaymentItem__Item-sc-kuidrs-1"
            ]
            
            for selector in specific_selectors:
                try:
                    elements = self.driver.find_elements(By.CSS_SELECTOR, selector)
                    for element in elements:
                        if element.is_displayed():
                            # التحقق من وجود النص "Card" داخل العنصر
                            element_text = element.text.lower()
                            if 'card' in element_text:
                                # التمرير للعنصر
                                self.driver.execute_script("arguments[0].scrollIntoView(true);", element)
                                time.sleep(1)
                                
                                # محاولة النقر
                                try:
                                    element.click()
                                    logger.info(f"✅ تم النقر على عنصر البطاقة المحدد: {element_text}")
                                    card_method_found = True
                                    time.sleep(2)
                                    return True
                                except:
                                    # محاولة النقر باستخدام JavaScript
                                    self.driver.execute_script("arguments[0].click();", element)
                                    logger.info(f"✅ تم النقر على عنصر البطاقة المحدد باستخدام JS: {element_text}")
                                    card_method_found = True
                                    time.sleep(2)
                                    return True
                except Exception as e:
                    logger.debug(f"لم يتم العثور على العنصر: {selector}")
                    continue
            
            # البحث باستخدام XPath
            for xpath in [s for s in card_method_selectors if s.startswith('//')]:
                try:
                    elements = self.driver.find_elements(By.XPATH, xpath)
                    for element in elements:
                        if element.is_displayed() and element.is_enabled():
                            # التحقق من أن العنصر يحتوي على كلمات متعلقة بالبطاقة
                            element_text = element.text.lower()
                            element_class = element.get_attribute('class') or ''
                            element_id = element.get_attribute('id') or ''
                            
                            card_keywords = ['card', 'credit', 'debit', 'visa', 'mastercard']
                            if any(keyword in element_text or keyword in element_class.lower() or keyword in element_id.lower() 
                                   for keyword in card_keywords):
                                
                                # التمرير للعنصر
                                self.driver.execute_script("arguments[0].scrollIntoView(true);", element)
                                time.sleep(1)
                                
                                # محاولة النقر
                                try:
                                    element.click()
                                    logger.info(f"✅ تم النقر على أيقونة البطاقة: {element_text}")
                                    card_method_found = True
                                    time.sleep(2)
                                    return True
                                except:
                                    # محاولة النقر باستخدام JavaScript
                                    self.driver.execute_script("arguments[0].click();", element)
                                    logger.info(f"✅ تم النقر على أيقونة البطاقة باستخدام JS: {element_text}")
                                    card_method_found = True
                                    time.sleep(2)
                                    return True
                except Exception as e:
                    continue
            
            # البحث باستخدام CSS Selectors
            for selector in [s for s in card_method_selectors if not s.startswith('//')]:
                try:
                    elements = self.driver.find_elements(By.CSS_SELECTOR, selector)
                    for element in elements:
                        if element.is_displayed() and element.is_enabled():
                            # التمرير للعنصر
                            self.driver.execute_script("arguments[0].scrollIntoView(true);", element)
                            time.sleep(1)
                            
                            # محاولة النقر
                            try:
                                element.click()
                                logger.info(f"✅ تم النقر على أيقونة البطاقة: {element.text}")
                                card_method_found = True
                                time.sleep(2)
                                return True
                            except:
                                # محاولة النقر باستخدام JavaScript
                                self.driver.execute_script("arguments[0].click();", element)
                                logger.info(f"✅ تم النقر على أيقونة البطاقة باستخدام JS: {element.text}")
                                card_method_found = True
                                time.sleep(2)
                                return True
                except Exception as e:
                    continue
            
            # البحث في جميع العناصر القابلة للنقر
            if not card_method_found:
                logger.info("🔍 البحث في جميع العناصر القابلة للنقر...")
                clickable_elements = self.driver.find_elements(By.XPATH, "//div[@onclick] | //button | //a | //span[@onclick] | //div[contains(@class, 'clickable')] | //div[contains(@class, 'selectable')] | //div[contains(@class, 'Item')]")
                
                for element in clickable_elements:
                    try:
                        if element.is_displayed():
                            element_text = element.text.lower()
                            element_class = element.get_attribute('class') or ''
                            element_id = element.get_attribute('id') or ''
                            data_testid = element.get_attribute('data-testid') or ''
                            
                            # البحث عن كلمات مفتاحية متعلقة بالبطاقة
                            card_keywords = ['card', 'credit', 'debit', 'visa', 'mastercard', 'payment method']
                            if any(keyword in element_text or keyword in element_class.lower() or keyword in element_id.lower() or keyword in data_testid.lower()
                                   for keyword in card_keywords):
                                
                                # التمرير للعنصر
                                self.driver.execute_script("arguments[0].scrollIntoView(true);", element)
                                time.sleep(1)
                                
                                # محاولة النقر
                                try:
                                    element.click()
                                    logger.info(f"✅ تم النقر على عنصر البطاقة: {element_text}")
                                    time.sleep(2)
                                    return True
                                except:
                                    continue
                    except:
                        continue
            
            if not card_method_found:
                logger.warning("⚠️ لم يتم العثور على أيقونة طريقة الدفع بالبطاقة")
                return False
            
        except Exception as e:
            logger.error(f"❌ خطأ في اختيار طريقة الدفع بالبطاقة: {e}")
            return False

    def select_specific_payment_card(self):
        """اختيار بطاقة الدفع المحددة من HTML المرفق"""
        logger.info("🎯 البحث عن بطاقة الدفع المحددة...")
        
        try:
            # انتظار تحميل الصفحة
            time.sleep(3)
            
            # محددات خاصة بالعنصر المرفق
            specific_card_selectors = [
                # بناءً على data-testid
                "div[data-testid='Visa']",
                
                # بناءً على الكلاس
                "div.newPaymentItem__Item-sc-kuidrs-1.dndelG",
                "div.newPaymentItem__Item-sc-kuidrs-1",
                "div[class*='newPaymentItem__Item']",
                
                # بناءً على النص "Card"
                "//div[contains(@class, 'newPaymentItem__Item') and contains(., 'Card')]",
                "//div[@data-testid='Visa' and contains(., 'Card')]",
                
                # بناءً على وجود أيقونات الدفع
                "//div[contains(@class, 'newPaymentItem__Item')]//div[contains(@class, 'PaymentIcons')]",
                "//div[@data-testid='Visa']//div[@data-doublerow='true']",
                
                # محددات عامة للعناصر المشابهة
                "div[class*='paymentItem']",
                "div[class*='PaymentItem']"
            ]
            
            logger.info("🔍 البحث عن العنصر المحدد...")
            
            for selector in specific_card_selectors:
                try:
                    if selector.startswith('//'):
                        # XPath selector
                        elements = self.driver.find_elements(By.XPATH, selector)
                    else:
                        # CSS selector
                        elements = self.driver.find_elements(By.CSS_SELECTOR, selector)
                    
                    for element in elements:
                        if element.is_displayed():
                            # التحقق من وجود النص "Card" أو أيقونات الدفع
                            element_text = element.text.lower()
                            element_html = element.get_attribute('innerHTML') or ''
                            
                            # البحث عن مؤشرات أن هذا هو عنصر البطاقة
                            card_indicators = [
                                'card' in element_text,
                                'visa' in element_html.lower(),
                                'mastercard' in element_html.lower(),
                                'payment' in element_html.lower(),
                                element.get_attribute('data-testid') == 'Visa'
                            ]
                            
                            if any(card_indicators):
                                logger.info(f"✅ تم العثور على عنصر البطاقة: {element.get_attribute('data-testid')} - {element_text}")
                                
                                # التمرير للعنصر
                                self.driver.execute_script("arguments[0].scrollIntoView(true);", element)
                                time.sleep(1)
                                
                                # محاولة النقر
                                try:
                                    element.click()
                                    logger.info("✅ تم النقر على بطاقة الدفع المحددة")
                                    time.sleep(2)
                                    return True
                                except ElementClickInterceptedException:
                                    # محاولة النقر باستخدام JavaScript
                                    self.driver.execute_script("arguments[0].click();", element)
                                    logger.info("✅ تم النقر على بطاقة الدفع المحددة باستخدام JS")
                                    time.sleep(2)
                                    return True
                                except Exception as click_error:
                                    logger.debug(f"فشل النقر على العنصر: {click_error}")
                                    continue
                            
                except Exception as e:
                    logger.debug(f"خطأ في البحث باستخدام المحدد {selector}: {e}")
                    continue
            
            logger.warning("⚠️ لم يتم العثور على بطاقة الدفع المحددة")
            return False
            
        except Exception as e:
            logger.error(f"❌ خطأ في اختيار بطاقة الدفع المحددة: {e}")
            return False

    def dismiss_overlays_and_popups(self):
        """إزالة النوافذ المنبثقة والعناصر المتداخلة"""
        logger.info("🔧 محاولة إزالة النوافذ المنبثقة والعناصر المتداخلة...")
        
        try:
            # قائمة محددات العناصر المتداخلة الشائعة
            overlay_selectors = [
                # نوافذ منبثقة عامة
                ".modal", ".popup", ".overlay", ".dialog",
                "[class*='modal']", "[class*='popup']", "[class*='overlay']",
                
                # قوائم التنقل والقوائم المنسدلة
                ".dropdown-menu", ".nav-menu", ".menu-dropdown",
                "[class*='dropdown']", "[class*='menu']",
                
                # إشعارات وتنبيهات
                ".notification", ".alert", ".toast", ".banner",
                "[class*='notification']", "[class*='alert']",
                
                # عناصر خاصة بـ Ding.com
                ".company-menu-wrap", "[data-testid='company-menu-wrap']",
                ".top-up-dropdown", "[data-testid*='dropdown']"
            ]
            
            overlays_removed = 0
            
            for selector in overlay_selectors:
                try:
                    elements = self.driver.find_elements(By.CSS_SELECTOR, selector)
                    for element in elements:
                        if element.is_displayed():
                            try:
                                # محاولة إخفاء العنصر
                                self.driver.execute_script("arguments[0].style.display = 'none';", element)
                                overlays_removed += 1
                                logger.debug(f"تم إخفاء عنصر: {selector}")
                            except:
                                continue
                except:
                    continue
            
            # إزالة العناصر التي قد تحجب النقر
            try:
                self.driver.execute_script("""
                    // إزالة pointer-events من العناصر المتداخلة
                    var elements = document.querySelectorAll('*');
                    for (var i = 0; i < elements.length; i++) {
                        var el = elements[i];
                        var style = window.getComputedStyle(el);
                        if (style.position === 'fixed' || style.position === 'absolute') {
                            if (style.zIndex > 1000) {
                                el.style.pointerEvents = 'none';
                            }
                        }
                    }
                """)
            except:
                pass
            
            if overlays_removed > 0:
                logger.info(f"✅ تم إزالة {overlays_removed} عنصر متداخل")
            else:
                logger.debug("لم يتم العثور على عناصر متداخلة")
                
        except Exception as e:
            logger.debug(f"خطأ في إزالة العناصر المتداخلة: {e}")

    def fill_payment_form(self):
        """ملء نموذج الدفع بالكامل مع معالجة محسنة"""
        logger.info("💳 بدء ملء نموذج الدفع...")
        
        try:
            # إزالة العناصر المتداخلة أولاً
            self.dismiss_overlays_and_popups()
            
            # أولاً: محاولة اختيار بطاقة الدفع المحددة
            logger.info("🔍 محاولة اختيار بطاقة الدفع المحددة...")
            card_selected = self.select_specific_payment_card()
            
            if not card_selected:
                # إذا فشل العثور على البطاقة المحددة، نجرب الطريقة العامة
                logger.info("🔄 جاري تجربة الطريقة العامة لاختيار البطاقة...")
                card_selected = self.select_card_payment_method()
                
                if not card_selected:
                    logger.warning("⚠️ لم يتم العثور على أيقونة البطاقة، سيتم المتابعة مع حقول الدفع")
            
            # انتظار تحميل حقول الدفع
            time.sleep(3)
            
            # العثور على حقول الدفع (سيقوم بالتبديل إلى iframe)
            fields = self.find_payment_fields()
            
            if not fields:
                logger.error("❌ لم يتم العثور على حقول الدفع")
                return False
            
            # إزالة العناصر المتداخلة داخل iframe أيضاً
            try:
                self.driver.execute_script("""
                    // إزالة العناصر المتداخلة داخل iframe
                    var overlays = document.querySelectorAll('.overlay, .modal, .popup, [class*="dropdown"]');
                    overlays.forEach(function(el) {
                        el.style.display = 'none';
                    });
                """)
            except:
                pass
            
            # استخدام المنطق الجديد المحسن لملء الحقول
            logger.info("🎯 بدء ملء حقول الدفع بالمنطق المحسن...")
            success_count = 0

            # 1. ملء رقم البطاقة أولاً
            logger.info("💳 البحث عن حقل رقم البطاقة...")
            card_number_field = self.find_specific_field('card_number')
            if card_number_field:
                if self.fill_card_number_safely(card_number_field, self.test_card_data['card_number']):
                    success_count += 1
                    self.test_results['filled_card_number'] = True
                    time.sleep(1)  # انتظار بعد ملء رقم البطاقة

            # 2. ملء تاريخ انتهاء الصلاحية
            logger.info("📅 البحث عن حقل تاريخ الانتهاء...")
            expiry_field = self.find_specific_field('expiry')
            if expiry_field:
                if self.fill_field_safely(expiry_field, self.test_card_data['expiry_full'], 'تاريخ الانتهاء'):
                    success_count += 1
                    self.test_results['filled_expiry'] = True
                    time.sleep(1)

            # 3. ملء اسم حامل البطاقة
            logger.info("👤 البحث عن حقل اسم حامل البطاقة...")
            name_field = self.find_specific_field('cardholder_name')

            # إذا لم نجد الحقل، نبحث بطريقة أخرى
            if not name_field:
                logger.info("🔍 البحث عن حقل الاسم بطريقة بديلة...")
                try:
                    # البحث عن حقول النص التي قد تكون للاسم
                    text_fields = self.driver.find_elements(By.CSS_SELECTOR, "input[type='text']")
                    for field in text_fields:
                        if field.is_displayed() and field.is_enabled():
                            # تحقق من أن الحقل فارغ أو يحتوي على placeholder للاسم
                            placeholder = (field.get_attribute('placeholder') or '').lower()
                            value = field.get_attribute('value') or ''

                            # إذا كان الحقل فارغ ولا يحتوي على أرقام في placeholder
                            if (not value and
                                ('name' in placeholder or
                                 placeholder == '' or
                                 'holder' in placeholder) and
                                not any(char.isdigit() for char in placeholder)):
                                name_field = field
                                logger.info(f"✅ تم العثور على حقل محتمل للاسم: {placeholder}")
                                break
                except Exception as e:
                    logger.debug(f"خطأ في البحث البديل عن حقل الاسم: {e}")

            if name_field:
                if self.fill_field_safely(name_field, self.test_card_data['name_on_card'], 'اسم حامل البطاقة'):
                    success_count += 1
                    self.test_results['filled_name_on_card'] = True
                    time.sleep(1)
            else:
                logger.warning("⚠️ لم يتم العثور على حقل اسم حامل البطاقة")

            # 4. ملء CVV أخيراً (لأنه قد يُمسح)
            logger.info("🔐 البحث عن حقل CVV...")
            cvv_field = self.find_specific_field('cvv')
            if cvv_field:
                if self.fill_field_safely(cvv_field, self.test_card_data['cvv'], 'CVV'):
                    success_count += 1
                    self.test_results['filled_cvv'] = True

            # التحقق النهائي من جميع الحقول
            logger.info("🔍 التحقق النهائي من جميع الحقول...")
            final_verification = self.verify_all_fields_filled()

            logger.info(f"📊 تم ملء {success_count} حقول بنجاح")

            # إنهاء الدالة هنا مع النتيجة الجديدة
            return final_verification and success_count >= 3

        except Exception as e:
            logger.error(f"❌ خطأ في ملء نموذج الدفع: {e}")
            return False
    
    def attempt_payment_submission(self):
        """محاولة إرسال نموذج الدفع مع معالجة محسنة"""
        logger.info("💰 محاولة إرسال نموذج الدفع...")
        
        try:
            # التأكد من العودة للمحتوى الرئيسي
            try:
                self.driver.switch_to.default_content()
            except:
                pass

            # إزالة العناصر المتداخلة قبل البحث عن زر الدفع
            self.dismiss_overlays_and_popups()

            # أولاً: البحث عن الزر الخارجي (الزر الفعلي للإرسال)
            logger.info("🔍 البحث عن زر الدفع الخارجي أولاً...")
            external_button_selectors = [
                "button#PayButton",  # الزر الرئيسي من HTML المرفق
                "button[name='PayButton']",
                "button.dn-pay-button",
                "button[class*='dn-pay-button']",
                "button[id*='PayButton']"
            ]

            pay_button = None

            # البحث عن الزر الخارجي
            for selector in external_button_selectors:
                try:
                    buttons = self.driver.find_elements(By.CSS_SELECTOR, selector)
                    for button in buttons:
                        if button.is_displayed() and button.is_enabled():
                            button_text = button.text.strip()
                            # التحقق من أن الزر يحتوي على "Pay USD" و "now" (تجنب الاعتماد على المبلغ المحدد)
                            if 'pay' in button_text.lower() and 'usd' in button_text.lower() and 'now' in button_text.lower():
                                pay_button = button
                                logger.info(f"✅ تم العثور على زر الدفع الخارجي: {button_text}")
                                break

                    if pay_button:
                        break
                except Exception as e:
                    logger.debug(f"خطأ في البحث عن الزر الخارجي باستخدام {selector}: {e}")
                    continue

            # إذا لم نجد الزر الخارجي، ابحث في جميع الأزرار الخارجية
            if not pay_button:
                logger.info("🔍 البحث في جميع الأزرار الخارجية...")
                try:
                    all_buttons = self.driver.find_elements(By.TAG_NAME, "button")
                    for button in all_buttons:
                        if button.is_displayed() and button.is_enabled():
                            button_text = button.text.strip().lower()
                            # البحث عن زر يحتوي على "pay" و "usd" و "now"
                            if 'pay' in button_text and 'usd' in button_text and 'now' in button_text:
                                pay_button = button
                                logger.info(f"✅ تم العثور على زر الدفع الخارجي: {button.text}")
                                break
                except Exception as e:
                    logger.debug(f"خطأ في البحث العام للأزرار الخارجية: {e}")

            # إذا لم نجد الزر الخارجي، ابحث داخل iframe
            if not pay_button:
                logger.info("🔍 لم يتم العثور على زر خارجي، البحث داخل iframe...")

                # التبديل إلى iframe
                if self.switch_to_payment_iframe():
                    # البحث عن زر الدفع بطرق متعددة داخل iframe
                    pay_button_selectors = [
                        # أزرار الدفع الرئيسية
                        "button[type='submit']",
                        ".pay-btn",
                        ".submit-btn",
                        ".payment-submit",
                        "button[class*='pay']",
                        "button[class*='submit']",

                        # أزرار خاصة بـ Ding.com
                        "[data-testid*='payment']",
                        "[data-testid*='submit']",
                        "[data-testid*='continue']",

                        # محددات إضافية
                        "button.primary",
                        "button.btn-primary",
                        "button[class*='primary']"
                    ]
                else:
                    logger.warning("⚠️ فشل في التبديل إلى iframe")
                    pay_button_selectors = []
            else:
                # إذا وجدنا الزر الخارجي، لا نحتاج للبحث داخل iframe
                pay_button_selectors = []
            
            # محاولة العثور على زر الدفع مع إعادة المحاولة
            max_attempts = 3
            pay_button = None
            
            for attempt in range(max_attempts):
                logger.info(f"محاولة العثور على زر الدفع ({attempt + 1}/{max_attempts})")
                
                # البحث باستخدام المحددات
                for selector in pay_button_selectors:
                    try:
                        buttons = WebDriverWait(self.driver, 5).until(
                            EC.presence_of_all_elements_located((By.CSS_SELECTOR, selector))
                        )
                        
                        for button in buttons:
                            if button.is_displayed() and button.is_enabled():
                                button_text = button.text.lower()
                                if any(word in button_text for word in ['pay', 'submit', 'complete', 'continue', 'proceed']):
                                    pay_button = button
                                    logger.info(f"✅ تم العثور على زر الدفع: {button_text}")
                                    break
                        
                        if pay_button:
                            break
                            
                    except:
                        continue
                
                if pay_button:
                    break
                    
                # إذا لم نجد الزر، نبحث في جميع الأزرار
                if not pay_button:
                    try:
                        logger.info("البحث في جميع الأزرار المرئية...")
                        all_buttons = self.driver.find_elements(By.TAG_NAME, "button")
                        for button in all_buttons:
                            if button.is_displayed() and button.is_enabled():
                                button_text = button.text.lower()
                                logger.info(f"  زر: '{button_text}'")

                                # البحث عن النص المحدد من النموذج المرفق
                                if ('pay' in button_text and 'usd' in button_text and 'now' in button_text) or \
                                   any(word in button_text for word in ['pay', 'submit', 'complete', 'continue', 'proceed']):
                                    pay_button = button
                                    logger.info(f"✅ تم العثور على زر الدفع في البحث العام: {button_text}")
                                    break
                    except:
                        pass
                
                if not pay_button and attempt < max_attempts - 1:
                    logger.info("⏳ انتظار قبل المحاولة التالية...")
                    time.sleep(2)
                    # إزالة العناصر المتداخلة مرة أخرى
                    self.dismiss_overlays_and_popups()
            
            if not pay_button:
                logger.error("❌ لم يتم العثور على زر الدفع")
                return False
            
            # التأكد من العودة للمحتوى الرئيسي قبل النقر (في حالة كان الزر خارجي)
            try:
                self.driver.switch_to.default_content()
                logger.info("🔄 تم العودة للمحتوى الرئيسي قبل النقر على الزر")
            except:
                pass

            # حفظ URL الحالي للمقارنة
            current_url = self.driver.current_url
            logger.info(f"URL قبل الدفع: {current_url}")

            # التمرير لزر الدفع بشكل محسن
            try:
                # التمرير مع هامش أمان
                self.driver.execute_script("""
                    arguments[0].scrollIntoView({
                        behavior: 'smooth',
                        block: 'center',
                        inline: 'center'
                    });
                """, pay_button)
                time.sleep(1)
            except:
                self.driver.execute_script("arguments[0].scrollIntoView(true);", pay_button)
                time.sleep(1)
            
            # محاولة النقر على زر الدفع بطرق متعددة
            logger.info(f"🔘 محاولة النقر على زر الدفع: {pay_button.text}")
            
            click_success = False
            click_methods = [
                ("النقر المباشر", lambda: pay_button.click()),
                ("JavaScript Click", lambda: self.driver.execute_script("arguments[0].click();", pay_button)),
                ("ActionChains", lambda: ActionChains(self.driver).move_to_element(pay_button).click().perform()),
                ("Force Click", lambda: self.driver.execute_script("""
                    arguments[0].dispatchEvent(new MouseEvent('click', {
                        view: window,
                        bubbles: true,
                        cancelable: true
                    }));
                """, pay_button))
            ]
            
            for method_name, click_method in click_methods:
                try:
                    logger.info(f"  🔹 محاولة {method_name}...")
                    click_method()
                    click_success = True
                    self.test_results['clicked_pay_button'] = True
                    logger.info(f"✅ تم النقر على زر الدفع باستخدام {method_name}")
                    break
                except Exception as e:
                    logger.debug(f"فشل {method_name}: {e}")
                    continue
            
            if not click_success:
                logger.error("❌ فشل في النقر على زر الدفع بجميع الطرق")
                return False
            
            # انتظار الاستجابة
            logger.info("⏳ انتظار الاستجابة...")
            time.sleep(5)
            
            # فحص التغييرات
            new_url = self.driver.current_url
            logger.info(f"URL بعد الدفع: {new_url}")
            
            # فحص رسائل الخطأ
            error_selectors = [
                ".error", ".alert-danger", ".error-message", "[class*='error']",
                ".invalid-feedback", ".field-error", ".validation-error",
                "[class*='invalid']", "[class*='fail']"
            ]
            
            errors_found = []
            for selector in error_selectors:
                try:
                    error_elements = self.driver.find_elements(By.CSS_SELECTOR, selector)
                    for error in error_elements:
                        if error.is_displayed() and error.text.strip():
                            errors_found.append(error.text.strip())
                except:
                    continue
            
            if errors_found:
                logger.info("⚠️ تم العثور على رسائل خطأ:")
                for error in errors_found:
                    logger.info(f"  - {error}")
                self.test_results['error_messages'] = errors_found
            
            # فحص التغيير في URL
            if new_url != current_url:
                logger.info("✅ تم تغيير URL - قد يكون تم إرسال النموذج")
                self.test_results['payment_submitted'] = True
                self.test_results['response_received'] = True
            else:
                logger.info("⚠️ لم يتغير URL - قد لم يتم إرسال النموذج")
            
            # فحص وجود رسائل نجاح
            success_selectors = [
                ".success", ".alert-success", ".confirmation", "[class*='success']",
                ".payment-success", ".payment-complete", "[class*='complete']"
            ]
            
            success_found = False
            for selector in success_selectors:
                try:
                    success_elements = self.driver.find_elements(By.CSS_SELECTOR, selector)
                    if success_elements and any(elem.is_displayed() for elem in success_elements):
                        success_found = True
                        break
                except:
                    continue
            
            if success_found:
                logger.info("✅ تم العثور على رسالة نجاح")
                self.test_results['payment_submitted'] = True
            
            self.test_results['final_url'] = new_url
            
            return True
            
        except Exception as e:
            logger.error(f"❌ خطأ في محاولة إرسال الدفع: {e}")
            return False
    
    def generate_final_report(self):
        """إنشاء التقرير النهائي"""
        logger.info("📊 تقرير نتائج اختبار صفحة الدفع:")
        logger.info("=" * 60)
        
        logger.info("🎯 نتائج الاختبار:")
        logger.info(f"  🍪 تحميل الكوكيز: {'✅ نجح' if self.test_results['cookies_loaded'] else '❌ فشل'} ({self.test_results['cookies_count']} كوكيز)")
        logger.info(f"  📄 الوصول لصفحة الملخص: {'✅ نجح' if self.test_results['reached_summary_page'] else '❌ فشل'}")
        logger.info(f"  🔘 النقر على Continue to payment: {'✅ نجح' if self.test_results['clicked_continue_payment'] else '❌ فشل'}")
        logger.info(f"  💳 الوصول لصفحة الدفع: {'✅ نجح' if self.test_results['reached_payment_page'] else '❌ فشل'}")
        logger.info(f"  💳 ملء رقم البطاقة: {'✅ نجح' if self.test_results['filled_card_number'] else '❌ فشل'}")
        logger.info(f"  📅 ملء تاريخ الانتهاء: {'✅ نجح' if self.test_results['filled_expiry'] else '❌ فشل'}")
        logger.info(f"  🔐 ملء CVV: {'✅ نجح' if self.test_results['filled_cvv'] else '❌ فشل'}")
        logger.info(f"  👤 ملء الاسم: {'✅ نجح' if self.test_results['filled_name'] else '❌ فشل'}")
        logger.info(f"  📮 ملء الرمز البريدي: {'✅ نجح' if self.test_results['filled_zip'] else '❌ فشل'}")
        logger.info(f"  🔘 النقر على زر الدفع: {'✅ نجح' if self.test_results['clicked_pay_button'] else '❌ فشل'}")
        logger.info(f"  📤 إرسال بيانات الدفع: {'✅ نجح' if self.test_results['payment_submitted'] else '❌ فشل'}")
        logger.info(f"  📨 استلام استجابة: {'✅ نجح' if self.test_results['response_received'] else '❌ فشل'}")
        
        if self.test_results['error_messages']:
            logger.info("⚠️ رسائل الخطأ المكتشفة:")
            for error in self.test_results['error_messages']:
                logger.info(f"  - {error}")
        
        logger.info(f"🌐 URL النهائي: {self.test_results['final_url']}")
        
        # تقييم مستوى الأمان
        filled_fields = sum([
            self.test_results['filled_card_number'],
            self.test_results['filled_expiry'],
            self.test_results['filled_cvv'],
            self.test_results['filled_name'],
            self.test_results['filled_zip']
        ])
        
        if not self.test_results['reached_payment_page']:
            security_level = "عالي جداً 🔒🔒🔒 - لم يصل لصفحة الدفع"
        elif filled_fields == 0:
            security_level = "عالي جداً 🔒🔒🔒 - لم ينجح في ملء أي حقل"
        elif not self.test_results['clicked_pay_button']:
            security_level = "عالي 🔒🔒 - ملء الحقول لكن لم ينقر على الدفع"
        elif not self.test_results['payment_submitted']:
            security_level = "متوسط إلى عالي 🔒 - نقر على الدفع لكن لم يرسل البيانات"
        else:
            security_level = "ضعيف ⚠️ - تم إرسال بيانات الدفع"
        
        logger.info(f"🎯 تقييم مستوى الأمان: {security_level}")
    
    def run_complete_test(self):
        """تشغيل الاختبار الكامل"""
        logger.info("🚀 بدء اختبار صفحة الدفع الكامل (محدث)...")
        
        try:
            # إعداد المتصفح
            self.setup_driver()
            
            # الانتقال لصفحة الملخص
            if not self.navigate_to_summary_page():
                logger.error("❌ فشل في الوصول لصفحة الملخص")
                self.generate_final_report()
                return False
            
            # النقر على Continue to payment
            if not self.click_continue_to_payment():
                logger.error("❌ فشل في النقر على Continue to payment")
                self.generate_final_report()
                return False
            
            # ملء نموذج الدفع
            if not self.fill_payment_form():
                logger.error("❌ فشل في ملء نموذج الدفع")
                self.generate_final_report()
                return False
            
            # محاولة إرسال الدفع
            self.attempt_payment_submission()
            
            # تقرير النتائج
            self.generate_final_report()
            
            # انتظار لرؤية النتائج
            logger.info("⏳ انتظار 15 ثانية لرؤية النتائج النهائية...")
            time.sleep(15)
            
            return True
            
        except Exception as e:
            logger.error(f"❌ خطأ في تشغيل الاختبار: {e}")
            return False
        
        finally:
            if self.driver:
                logger.info("🔚 إغلاق المتصفح...")
                self.driver.quit()

def main():
    """الدالة الرئيسية"""
    tester = DingPaymentTesterUpdated()
    tester.run_complete_test()

if __name__ == "__main__":
    main()

